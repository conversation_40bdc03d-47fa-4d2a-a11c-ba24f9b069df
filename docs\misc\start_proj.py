#!/usr/bin/env python3
"""
Project Scaffolding Generator

This script automatically generates a complete project structure for a Python package
with multiple modules. It creates directory structures, initial Python files with
placeholder code, configuration files, and documentation.

Usage:
    python start_proj.py [--project-name NAME] [--author <PERSON><PERSON><PERSON>] [--email EMAIL]

Example:
    python start_proj.py --project-name "my_awesome_tool" --author "<PERSON>" --email "<EMAIL>"
"""

import os
import argparse
import datetime
from pathlib import Path


def parse_arguments():
    """
    Parse command line arguments for project configuration.
    
    Returns:
        argparse.Namespace: Object containing the parsed arguments
    """
    parser = argparse.ArgumentParser(description='Generate a Python project scaffold')
    parser.add_argument('--project-name', type=str, default='pdf_to_md_project',
                        help='Name of the project (default: pdf_to_md_project)')
    parser.add_argument('--author', type=str, default='Your Name',
                        help='Author name for project metadata')
    parser.add_argument('--email', type=str, default='<EMAIL>',
                        help='Author email for project metadata')
    return parser.parse_args()


def create_directory_structure(project_name):
    """
    Create the directory structure for the project.
    
    Args:
        project_name (str): Name of the project
        
    Returns:
        dict: Dictionary of created paths for further reference
    """
    paths = {
        'root': project_name,
        'pdf_to_md': os.path.join(project_name, 'pdf_to_md'),
        'md_refiner': os.path.join(project_name, 'md_refiner'),
        'pdf_to_md_templates': os.path.join(project_name, 'pdf_to_md', 'templates'),
        'pdf_to_md_static': os.path.join(project_name, 'pdf_to_md', 'static'),
        'pdf_to_md_tests': os.path.join(project_name, 'pdf_to_md', 'tests'),
        'md_refiner_tests': os.path.join(project_name, 'md_refiner', 'tests'),
    }
    
    # Create all directories
    for path in paths.values():
        os.makedirs(path, exist_ok=True)
        print(f"Created directory: {path}")
        
    return paths


def write_file(file_path, content):
    """
    Write content to a file with UTF-8 encoding.
    
    Args:
        file_path (str): Path to the file
        content (str): Content to write to the file
    """
    with open(file_path, 'w', encoding="utf-8") as f:
        f.write(content)
    print(f"Created file: {file_path}")


def create_pdf_to_md_files(paths):
    """
    Create the Python files for the pdf_to_md module.
    
    Args:
        paths (dict): Dictionary of project paths
    """
    # Create __init__.py
    init_content = '''"""
PDF to Markdown Converter Module

This module provides functionality to convert PDF documents to Markdown format.
"""

__version__ = "0.1.0"
'''
    write_file(os.path.join(paths['pdf_to_md'], '__init__.py'), init_content)
    
    # Create __main__.py
    main_content = '''"""
Command-line interface for the PDF to Markdown converter.
"""

import argparse
import sys
from .parser import parse_pdf
from .converter import convert_to_markdown

def main():
    """Main entry point for the PDF to Markdown converter."""
    parser = argparse.ArgumentParser(description="Convert PDF to Markdown")
    parser.add_argument("input", help="Input PDF file")
    parser.add_argument("output", help="Output Markdown file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    try:
        if args.verbose:
            print(f"Parsing PDF: {args.input}")
        
        # Pass the verbose flag to parse_pdf
        pages = parse_pdf(args.input, verbose=args.verbose)
        
        if args.verbose:
            print("Converting to Markdown...")
        
        markdown = convert_to_markdown(pages)
        
        if args.verbose:
            print(f"Saving Markdown to: {args.output}")
        
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(markdown)
        
        if args.verbose:
            print("Conversion complete.")
    
    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    write_file(os.path.join(paths['pdf_to_md'], '__main__.py'), main_content)
    
    # Create parser.py
    parser_content = '''"""
PDF parsing module using PyMuPDF.
"""

import fitz  # PyMuPDF

def parse_pdf(file_path, verbose=False):
    """
    Parse a PDF file and extract text content from each page.
    
    Args:
        file_path (str): Path to the PDF file
        verbose (bool): Whether to print verbose output
        
    Returns:
        list: List of strings containing text content from each page
    """
    # Open the PDF file
    document = fitz.open(file_path)
    content = []
    
    # Iterate through each page
    for page_num in range(len(document)):
        if verbose:
            print(f"Processing page {page_num + 1}/{len(document)}")
            
        page = document.load_page(page_num)
        text = page.get_text("text")
        content.append(text)
    
    return content
'''
    write_file(os.path.join(paths['pdf_to_md'], 'parser.py'), parser_content)
    
    # Create converter.py
    converter_content = '''"""
Module for converting parsed PDF content to Markdown format.
"""

def convert_to_markdown(pdf_content, page_separator="\\n\\n"):
    """
    Converts a list of strings (PDF page contents) into a single Markdown-formatted string,
    with page number labeled explicitly.
    
    Args:
        pdf_content (list of str): Text content from each page of the PDF.
        page_separator (str): String to separate pages in the Markdown output.
    
    Returns:
        str: Combined Markdown-formatted string with page labels.
    """
    # Check for empty input
    if not pdf_content:
        return ""
    
    markdown_content = ""
    
    # Combine each page's content with spacing between pages
    for i, page in enumerate(pdf_content, 1):  # Start page numbering from 1
        # Clean up the page content
        page_text = page.strip()
        
        # Add page marker and content
        markdown_content += f"---\\n## Page {i}\\n\\n{page_text}{page_separator}"
    
    return markdown_content
'''
    write_file(os.path.join(paths['pdf_to_md'], 'converter.py'), converter_content)
    
    # Create utils.py
    utils_content = '''"""
Utility functions for the PDF to Markdown converter.
"""

import os

def validate_pdf_path(file_path):
    """
    Validate that the given path points to an existing PDF file.
    
    Args:
        file_path (str): Path to the PDF file
        
    Returns:
        bool: True if the file exists and has a .pdf extension
        
    Raises:
        FileNotFoundError: If the file does not exist
        ValueError: If the file does not have a .pdf extension
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not file_path.lower().endswith('.pdf'):
        raise ValueError(f"File is not a PDF: {file_path}")
    
    return True

def ensure_output_dir(output_path):
    """
    Ensure that the directory for the output file exists.
    
    Args:
        output_path (str): Path to the output file
        
    Returns:
        bool: True if the directory exists or was created
    """
    output_dir = os.path.dirname(output_path)
    
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    return True
'''
    write_file(os.path.join(paths['pdf_to_md'], 'utils.py'), utils_content)
    
    # Create test file
    test_parser_content = '''"""
Unit tests for the PDF parser module.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import sys

# Add parent directory to path to import the module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from parser import parse_pdf

class TestPDFParser(unittest.TestCase):
    """Test cases for the PDF parser module."""
    
    @patch('fitz.open')
    def test_parse_pdf(self, mock_open):
        """Test that parse_pdf correctly extracts text from PDF pages."""
        # Set up mock document
        mock_doc = MagicMock()
        mock_page = MagicMock()
        mock_page.get_text.return_value = "Test content"
        mock_doc.load_page.return_value = mock_page
        mock_doc.__len__.return_value = 2
        mock_open.return_value = mock_doc
        
        # Call the function
        result = parse_pdf("test.pdf")
        
        # Verify the results
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], "Test content")
        self.assertEqual(result[1], "Test content")
        
        # Verify the mock was called correctly
        mock_open.assert_called_once_with("test.pdf")
        
if __name__ == '__main__':
    unittest.main()
'''
    write_file(os.path.join(paths['pdf_to_md_tests'], 'test_parser.py'), test_parser_content)


def create_md_refiner_files(paths):
    """
    Create the Python files for the md_refiner module.
    
    Args:
        paths (dict): Dictionary of project paths
    """
    # Create __init__.py
    init_content = '''"""
Markdown Refiner Module

This module provides functionality to clean and refine Markdown files.
"""

__version__ = "0.1.0"
'''
    write_file(os.path.join(paths['md_refiner'], '__init__.py'), init_content)
    
    # Create __main__.py
    main_content = '''"""
Command-line interface for the Markdown refiner.
"""

import argparse
import os
from .processor import process_markdown

def main():
    """
    CLI entry point for the md_refiner tool.
    Parses command-line arguments, processes the Markdown file, and writes the cleaned output.
    """
    parser = argparse.ArgumentParser(description='Clean and refine Markdown files.')
    parser.add_argument('input', type=str, help='Input Markdown file')
    parser.add_argument('output', type=str, help='Output Markdown file')
    parser.add_argument('--remove-headers', action='store_true', help='Remove repeated headers')
    parser.add_argument('--remove-footers', action='store_true', help='Remove repeated footers')
    parser.add_argument('--remove-page-numbers', action='store_true', help='Remove page numbers')
    parser.add_argument('--remove-toc', action='store_true', help='Remove table of contents')
    parser.add_argument('--advanced-cleanup', action='store_true', help='Apply advanced regex-based cleanup')
    parser.add_argument('--convert-tables', action='store_true', help='Convert text tables to Markdown tables')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    try:
        if args.verbose:
            print(f"Processing Markdown file: {args.input}")
            print(f"Options: headers={args.remove_headers}, footers={args.remove_footers}, "
                  f"page_numbers={args.remove_page_numbers}, toc={args.remove_toc}, "
                  f"advanced={args.advanced_cleanup}, tables={args.convert_tables}")
        
        cleaned_content = process_markdown(
            args.input,
            remove_headers=args.remove_headers,
            remove_footers=args.remove_footers,
            remove_page_numbers=args.remove_page_numbers,
            remove_toc=args.remove_toc,
            advanced_cleanup=args.advanced_cleanup,
            convert_tables=args.convert_tables,
            verbose=args.verbose
        )

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(args.output)
        if output_dir:  # Only create directory if path is not empty
            os.makedirs(output_dir, exist_ok=True)

        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        if args.verbose:
            print(f"Markdown file cleaned and saved to {args.output}")

    except Exception as e:
        print(f"[ERROR] >> {str(e)}")

if __name__ == '__main__':
    main()
'''
    write_file(os.path.join(paths['md_refiner'], '__main__.py'), main_content)
    
    # Create processor.py
    processor_content = '''"""
Module for processing and refining Markdown content.
"""

import re
import os

def process_markdown(input_file, remove_headers=False, remove_footers=False, 
                    remove_page_numbers=False, remove_toc=False, 
                    advanced_cleanup=False, convert_tables=False, verbose=False):
    """
    Process a Markdown file to clean and refine its content.
    
    Args:
        input_file (str): Path to the input Markdown file
        remove_headers (bool): Whether to remove repeated headers
        remove_footers (bool): Whether to remove repeated footers
        remove_page_numbers (bool): Whether to remove page numbers
        remove_toc (bool): Whether to remove table of contents entries
        advanced_cleanup (bool): Whether to apply advanced regex-based cleanup
        convert_tables (bool): Whether to convert text tables to Markdown tables
        verbose (bool): Whether to print verbose output
        
    Returns:
        str: Cleaned and refined Markdown content
    """
    # Read the input file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if verbose:
        print(f"Read {len(content)} characters from {input_file}")
    
    # Apply the requested transformations
    if remove_headers:
        if verbose:
            print("Removing headers...")
        content = remove_repeated_headers(content)
    
    if remove_footers:
        if verbose:
            print("Removing footers...")
        content = remove_repeated_footers(content)
    
    if remove_page_numbers:
        if verbose:
            print("Removing page numbers...")
        content = remove_page_numbers_from_text(content)
    
    if remove_toc:
        if verbose:
            print("Removing table of contents...")
        content = remove_toc_entries(content)
    
    if advanced_cleanup:
        if verbose:
            print("Applying advanced cleanup...")
        content = apply_advanced_cleaning(content)
    
    if convert_tables:
        if verbose:
            print("Converting tables...")
        content = convert_text_tables_to_markdown(content)
    
    return content

def remove_repeated_headers(content):
    """
    Remove repeated headers from the Markdown content.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Markdown content with repeated headers removed
    """
    # Placeholder implementation
    # In a real implementation, this would identify and remove repeated headers
    # across page boundaries
    return content

def remove_repeated_footers(content):
    """
    Remove repeated footers from the Markdown content.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Markdown content with repeated footers removed
    """
    # Placeholder implementation
    return content

def remove_page_numbers_from_text(content):
    """
    Remove standalone page numbers from the Markdown content.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Markdown content with page numbers removed
    """
    # Placeholder implementation
    # This would identify and remove standalone page numbers
    return content

def remove_toc_entries(content):
    """
    Remove table of contents entries from the Markdown content.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Markdown content with TOC entries removed
    """
    # Placeholder implementation
    return content

def apply_advanced_cleaning(content):
    """
    Apply advanced regex-based cleanup to the Markdown content.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Cleaned Markdown content
    """
    # Placeholder implementation
    # This would apply various regex transformations to clean up the content
    return content

def convert_text_tables_to_markdown(content):
    """
    Convert text-based tables to Markdown tables.
    
    Args:
        content (str): Markdown content
        
    Returns:
        str: Markdown content with text tables converted to Markdown tables
    """
    # Placeholder implementation
    # This would identify text-based tables and convert them to Markdown format
    return content
'''
    write_file(os.path.join(paths['md_refiner'], 'processor.py'), processor_content)
    
    # Create utils.py
    utils_content = '''"""
Utility functions for the Markdown refiner.
"""

import os

def load_markdown(file_path):
    """
    Load Markdown content from a file.
    
    Args:
        file_path (str): Path to the Markdown file
        
    Returns:
        str: Content of the Markdown file
        
    Raises:
        FileNotFoundError: If the file does not exist
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def save_markdown(content, file_path):
    """
    Save Markdown content to a file.
    
    Args:
        content (str): Markdown content to save
        file_path (str): Path to the output file
        
    Returns:
        bool: True if the file was saved successfully
    """
    # Ensure the output directory exists
    output_dir = os.path.dirname(file_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True
'''
    write_file(os.path.join(paths['md_refiner'], 'utils.py'), utils_content)
    
    # Create test file
    test_processor_content = '''"""
Unit tests for the Markdown processor module.
"""'''


import unittest
from unittest.mock import patch, mock_open
import os
import sys

# Add parent directory to path to import the module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from processor import process_markdown, remove_repeated_headers

class TestMarkdownProcessor(unittest.TestCase):
    """Test cases for the Markdown processor module."""
    
    @patch('builtins.open', new_callable=mock_open, read_data="Test Markdown content")
    def test_process_markdown(self, mock_file):
        """Test that process_markdown correctly processes Markdown content."""
        # Call the function
        result = process_markdown("test.md")
        
        # Verify the results
        self.assertEqual(result, "Test Markdown content")
        
        # Verify the mock was called correctly
        mock_file.assert_called_with("test.md", 'r', encoding='utf-8')
        
    def test_remove_repeated_headers(self):
        """Test that remove_repeated_headers correctly removes repeated headers."""
        # Test input
        test_input = "# Header\\n\\nContent\\n\\n---\\n## Header\\n\\nMore content"
        expected_output = "# Header\\n\\nContent\\n\\n---\\n## Header\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(test_input), expected_output)
        
        # Test with no headers
        no_headers_input = "Content without headers"
        self.assertEqual(remove_repeated_headers(no_headers_input), no_headers_input)
        
        # Test with multiple headers
        multiple_headers_input = "# Header 1\\n\\nContent 1\\n\\n---\\n## Header 1\\n\\nContent 1\\n\\n---\\n## Header 2\\n\\nContent 2"
        expected_output = "# Header 1\\n\\nContent 1\\n\\n---\\n## Header 1\\n\\nContent 1\\n\\n---\\n## Header 2\\n\\nContent 2"
        self.assertEqual(remove_repeated_headers(multiple_headers_input), expected_output)
        
        # Test with case-insensitive headers
        case_insensitive_input = "# Header\\n\\nContent\\n\\n---\\n## header\\n\\nMore content"
        expected_output = "# Header\\n\\nContent\\n\\n---\\n## Header\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(case_insensitive_input), expected_output)
        
        # Test with headers on the same line
        same_line_input = "# Header 1 # Header 2\\n\\nContent"
        expected_output = "# Header 1 # Header 2\\n\\nContent"
        self.assertEqual(remove_repeated_headers(same_line_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        self.assertEqual(remove_repeated_headers(mixed_levels_input), expected_output)
        
        # Test with headers on the same line and different levels
        mixed_levels_input = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
        expected_output = "# Header 1 # Header 2\\n\\nContent\\n\\n---\\n## Header 1\\n\\nMore content"
""")
