"""
PDF to Markdown converter module.
"""

from .parser import parse_pdf
from .converter import convert_to_markdown

def convert_pdf_to_md(input_file, output_file, verbose=False):
    """
    Convert a PDF file to Markdown.
    
    Args:
        input_file (str): Path to the input PDF file.
        output_file (str): Path to save the output Markdown file.
        verbose (bool): Whether to print verbose output.
    
    Returns:
        None
    """
    if verbose:
        print(f"Parsing PDF: {input_file}")
    
    # Parse the PDF
    pages = parse_pdf(input_file, verbose=verbose)
    
    if verbose:
        print("Converting to Markdown...")
    
    # Convert to Markdown
    markdown = convert_to_markdown(pages, verbose=verbose)
    
    if verbose:
        print(f"Saving Markdown to: {output_file}")
    
    # Save the Markdown
    from ..common.utils import save_markdown
    save_markdown(output_file, markdown, verbose=verbose)
    
    if verbose:
        print("Conversion complete.")