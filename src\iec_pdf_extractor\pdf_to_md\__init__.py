"""
PDF to Markdown converter module with advanced PyMuPDF features.
"""

from .parser import parse_pdf, parse_pdf_legacy
from .converter import convert_to_markdown
import os
from typing import Optional

def convert_pdf_to_md(input_file: str, output_file: str, verbose: bool = False,
                     extract_tables: bool = True, extract_images: bool = True,
                     extract_annotations: bool = True, extract_metadata: bool = True,
                     text_format: str = "text", output_dir: Optional[str] = None,
                     include_tables_in_md: bool = True, include_images_in_md: bool = True,
                     include_annotations_in_md: bool = True, include_links_in_md: bool = True,
                     include_metadata_in_md: bool = True) -> dict:
    """
    Convert a PDF file to Markdown with comprehensive PyMuPDF features.

    Args:
        input_file (str): Path to the input PDF file.
        output_file (str): Path to save the output Markdown file.
        verbose (bool): Whether to print verbose output.
        extract_tables (bool): Whether to extract tables and save as CSV.
        extract_images (bool): Whether to extract images.
        extract_annotations (bool): Whether to extract annotations and links.
        extract_metadata (bool): Whether to extract document metadata.
        text_format (str): Text extraction format ('text', 'dict', 'json', 'xml', 'html', 'blocks', 'words').
        output_dir (str): Directory to save extracted files. If None, uses output file directory.
        include_tables_in_md (bool): Include table references in markdown.
        include_images_in_md (bool): Include image references in markdown.
        include_annotations_in_md (bool): Include annotations in markdown.
        include_links_in_md (bool): Include links in markdown.
        include_metadata_in_md (bool): Include document metadata in markdown.

    Returns:
        dict: Comprehensive extraction results including file paths and statistics.
    """
    if verbose:
        print(f"Parsing PDF with advanced features: {input_file}")

    # Setup output directory
    if output_dir is None:
        output_dir = os.path.dirname(output_file) or "."

    # Parse the PDF with comprehensive extraction
    pdf_data = parse_pdf(
        input_file,
        verbose=verbose,
        extract_tables=extract_tables,
        extract_images=extract_images,
        extract_annotations=extract_annotations,
        extract_metadata=extract_metadata,
        text_format=text_format,
        output_dir=output_dir
    )

    if verbose:
        print("Converting to comprehensive Markdown...")

    # Convert to Markdown
    markdown = convert_to_markdown(
        pdf_data,
        verbose=verbose,
        include_tables=include_tables_in_md,
        include_images=include_images_in_md,
        include_annotations=include_annotations_in_md,
        include_links=include_links_in_md,
        include_metadata=include_metadata_in_md
    )

    if verbose:
        print(f"Saving Markdown to: {output_file}")

    # Save the Markdown
    from ..common.utils import save_markdown
    save_markdown(output_file, markdown, verbose=verbose)

    # Prepare results summary
    results = {
        "input_file": input_file,
        "output_file": output_file,
        "output_dir": output_dir,
        "pages_processed": len(pdf_data.get("pages", [])),
        "tables_found": len(pdf_data.get("tables", [])),
        "images_found": len(pdf_data.get("images", [])),
        "annotations_found": len(pdf_data.get("annotations", [])),
        "links_found": len(pdf_data.get("links", [])),
        "csv_files": [table.get("csv_file") for table in pdf_data.get("tables", []) if table.get("csv_file")],
        "image_files": [img.get("image_file") for img in pdf_data.get("images", []) if img.get("image_file")],
        "metadata": pdf_data.get("metadata", {}),
        "toc": pdf_data.get("toc", [])
    }

    if verbose:
        print("Advanced conversion complete:")
        print(f"  - {results['pages_processed']} pages processed")
        print(f"  - {results['tables_found']} tables extracted")
        print(f"  - {results['images_found']} images extracted")
        print(f"  - {results['annotations_found']} annotations found")
        print(f"  - {results['links_found']} links found")
        if results['csv_files']:
            print(f"  - CSV files: {', '.join(results['csv_files'])}")
        if results['image_files']:
            print(f"  - Image files: {', '.join(results['image_files'])}")

    return results


def convert_pdf_to_md_legacy(input_file: str, output_file: str, verbose: bool = False) -> None:
    """
    Legacy function that maintains the original API for backward compatibility.

    Args:
        input_file (str): Path to the input PDF file.
        output_file (str): Path to save the output Markdown file.
        verbose (bool): Whether to print verbose output.

    Returns:
        None
    """
    if verbose:
        print(f"Parsing PDF (legacy mode): {input_file}")

    # Parse the PDF using legacy method
    pages = parse_pdf_legacy(input_file, verbose=verbose)

    if verbose:
        print("Converting to Markdown...")

    # Convert to Markdown
    markdown = convert_to_markdown(pages, verbose=verbose)

    if verbose:
        print(f"Saving Markdown to: {output_file}")

    # Save the Markdown
    from ..common.utils import save_markdown
    save_markdown(output_file, markdown, verbose=verbose)

    if verbose:
        print("Conversion complete.")