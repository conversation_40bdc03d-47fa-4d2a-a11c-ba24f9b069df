# Generate pyproject.toml file for the project
pyproject_toml_content = """
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "iec-pdf-extracter"
version = "0.1.0"
description = "A toolchain for converting PDF documents into clean, structured Markdown."
readme = "README.md"
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
license = { text = "MIT" }
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Text Processing :: Markup",
    "Topic :: Utilities"
]
dependencies = [
    "pymupdf>=1.25.0"
]

[project.urls]
"Homepage" = "https://github.com/yourusername/iec-pdf-extracter"
"Bug Tracker" = "https://github.com/yourusername/iec-pdf-extracter/issues"

[project.scripts]
iec-pdf-extracter = "iec_pdf_extracter.cli:main"

[tool.setuptools]
package-dir = {"" = "src"}
packages = ["iec_pdf_extracter", "iec_pdf_extracter.pdf_to_md", "iec_pdf_extracter.md_refiner", "iec_pdf_extracter.common"]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0"
]
"""

# Save the pyproject.toml content to a file
with open("pyproject.toml", "w") as f:
    f.write(pyproject_toml_content)

print("pyproject.toml file has been generated successfully.")





