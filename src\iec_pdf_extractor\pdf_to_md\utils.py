import os

def validate_pdf_path(file_path):
    """
    Validates that the given file path exists and is a PDF.
    
    Args:
        file_path (str): Path to the PDF file.
    
    Raises:
        FileNotFoundError: If the file does not exist.
        ValueError: If the file is not a PDF.
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    if not file_path.lower().endswith(".pdf"):
        raise ValueError("Input file must be a PDF.")

def ensure_output_dir(path):
    """
    Ensures the directory for a given path exists.
    
    Args:
        path (str): A directory or file path.
    """
    dir_path = path if os.path.isdir(path) else os.path.dirname(path)
    if dir_path:
        os.makedirs(dir_path, exist_ok = True)

def save_markdown(markdown_content, output_path):
    """
    Saves the Markdown content to a file path.
    
    Args:
        markdown_content (str): The Markdown content to save.
        output_dir (str): Full path to the output Markdown file.
    """
    ensure_output_dir(os.path.dirname(output_path))
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(markdown_content)
   
def log(message, verbose=False):
    """
    Prints a message if verbose mode is enabled.
    
    Args:
        message (str): The message to print.
        verbose (bool): Whether to print the message.
    """
    if verbose:
        print(message)
