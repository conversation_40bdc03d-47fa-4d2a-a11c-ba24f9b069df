#!/usr/bin/env python3
"""
Advanced PDF Extraction Example

This script demonstrates the full capabilities of the IEC PDF Extractor
with comprehensive PyMuPDF features including table extraction, image extraction,
annotation processing, and structured text analysis.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from iec_pdf_extractor.pdf_to_md import convert_pdf_to_md
from iec_pdf_extractor.pdf_to_md.parser import parse_pdf
from iec_pdf_extractor.md_refiner import refine_markdown


def demonstrate_basic_extraction(pdf_path: str, output_dir: str):
    """Demonstrate basic PDF to Markdown conversion with all features."""
    print("=" * 60)
    print("BASIC EXTRACTION WITH ALL FEATURES")
    print("=" * 60)
    
    output_file = os.path.join(output_dir, "basic_extraction.md")
    
    results = convert_pdf_to_md(
        input_file=pdf_path,
        output_file=output_file,
        verbose=True,
        extract_tables=True,
        extract_images=True,
        extract_annotations=True,
        extract_metadata=True,
        text_format="dict",  # Get structured text data
        output_dir=output_dir,
        include_tables_in_md=True,
        include_images_in_md=True,
        include_annotations_in_md=True,
        include_links_in_md=True,
        include_metadata_in_md=True
    )
    
    print(f"\nExtraction Results:")
    print(f"  - Pages processed: {results['pages_processed']}")
    print(f"  - Tables found: {results['tables_found']}")
    print(f"  - Images found: {results['images_found']}")
    print(f"  - Annotations found: {results['annotations_found']}")
    print(f"  - Links found: {results['links_found']}")
    
    if results['csv_files']:
        print(f"  - CSV files created: {', '.join(results['csv_files'])}")
    if results['image_files']:
        print(f"  - Image files created: {', '.join(results['image_files'])}")
    
    return results


def demonstrate_table_only_extraction(pdf_path: str, output_dir: str):
    """Demonstrate table-only extraction."""
    print("\n" + "=" * 60)
    print("TABLE-ONLY EXTRACTION")
    print("=" * 60)
    
    tables_dir = os.path.join(output_dir, "tables_only")
    os.makedirs(tables_dir, exist_ok=True)
    
    # Extract only tables
    pdf_data = parse_pdf(
        pdf_path,
        verbose=True,
        extract_tables=True,
        extract_images=False,
        extract_annotations=False,
        extract_metadata=False,
        text_format="text",
        output_dir=tables_dir
    )
    
    tables = pdf_data.get("tables", [])
    print(f"\nFound {len(tables)} tables in the document")
    
    # Create a summary
    if tables:
        from iec_pdf_extractor.pdf_to_md.converter import _generate_tables_summary
        summary_content = _generate_tables_summary(tables, True)
        
        summary_file = os.path.join(tables_dir, "tables_summary.md")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"# Tables Extracted from {os.path.basename(pdf_path)}\n\n")
            f.write(summary_content)
        
        print(f"Created summary file: {summary_file}")
    
    return tables


def demonstrate_structured_text_analysis(pdf_path: str, output_dir: str):
    """Demonstrate structured text extraction and analysis."""
    print("\n" + "=" * 60)
    print("STRUCTURED TEXT ANALYSIS")
    print("=" * 60)
    
    # Extract with different text formats
    formats = ["text", "dict", "json", "blocks", "words"]
    
    for text_format in formats:
        print(f"\nExtracting with format: {text_format}")
        
        format_dir = os.path.join(output_dir, f"format_{text_format}")
        os.makedirs(format_dir, exist_ok=True)
        
        pdf_data = parse_pdf(
            pdf_path,
            verbose=False,  # Reduce verbosity for multiple extractions
            extract_tables=False,
            extract_images=False,
            extract_annotations=False,
            extract_metadata=False,
            text_format=text_format,
            output_dir=format_dir
        )
        
        # Save first page's structured text for analysis
        if pdf_data["pages"]:
            first_page = pdf_data["pages"][0]
            
            # Save the text content
            text_file = os.path.join(format_dir, f"page_001_{text_format}.txt")
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write(first_page["text"])
            
            # Save structured data if available
            if first_page.get("structured_text"):
                import json
                struct_file = os.path.join(format_dir, f"page_001_{text_format}_structured.json")
                with open(struct_file, 'w', encoding='utf-8') as f:
                    json.dump(first_page["structured_text"], f, indent=2, ensure_ascii=False)
            
            print(f"  - Saved: {text_file}")


def demonstrate_complete_pipeline(pdf_path: str, output_dir: str):
    """Demonstrate the complete processing pipeline."""
    print("\n" + "=" * 60)
    print("COMPLETE PROCESSING PIPELINE")
    print("=" * 60)
    
    # Step 1: Extract with all features
    temp_file = os.path.join(output_dir, "temp_extraction.md")
    
    results = convert_pdf_to_md(
        input_file=pdf_path,
        output_file=temp_file,
        verbose=True,
        extract_tables=True,
        extract_images=True,
        extract_annotations=True,
        extract_metadata=True,
        text_format="dict",
        output_dir=output_dir,
        include_tables_in_md=True,
        include_images_in_md=True,
        include_annotations_in_md=True,
        include_links_in_md=True,
        include_metadata_in_md=True
    )
    
    # Step 2: Refine the markdown
    final_file = os.path.join(output_dir, "final_processed.md")
    
    refine_markdown(
        input_file=temp_file,
        output_file=final_file,
        remove_headers=True,
        remove_footers=True,
        remove_page_numbers=True,
        fix_toc_formatting=True,
        fix_internal_headings=True,
        remove_page_markers=True,
        collapse_blank_lines=True,
        extract_key_sections=True,  # Focus on IEC sections 6-9
        verbose=True
    )
    
    # Clean up temp file
    try:
        os.remove(temp_file)
        print(f"Removed temporary file: {temp_file}")
    except:
        print(f"Warning: Could not remove temporary file: {temp_file}")
    
    print(f"\nFinal processed file: {final_file}")
    return final_file


def main():
    """Main demonstration function."""
    if len(sys.argv) != 2:
        print("Usage: python advanced_extraction_example.py <pdf_file>")
        print("\nThis script demonstrates all advanced PyMuPDF features of the IEC PDF Extractor.")
        print("It will create an 'example_output' directory with various extraction results.")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    # Create output directory
    output_dir = "example_output"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Processing PDF: {pdf_path}")
    print(f"Output directory: {output_dir}")
    
    try:
        # Demonstrate different extraction methods
        basic_results = demonstrate_basic_extraction(pdf_path, output_dir)
        tables = demonstrate_table_only_extraction(pdf_path, output_dir)
        demonstrate_structured_text_analysis(pdf_path, output_dir)
        final_file = demonstrate_complete_pipeline(pdf_path, output_dir)
        
        print("\n" + "=" * 60)
        print("DEMONSTRATION COMPLETE")
        print("=" * 60)
        print(f"\nCheck the '{output_dir}' directory for all extracted content:")
        print(f"  - Basic extraction: {output_dir}/basic_extraction.md")
        print(f"  - Table-only extraction: {output_dir}/tables_only/")
        print(f"  - Structured text analysis: {output_dir}/format_*/")
        print(f"  - Final processed file: {final_file}")
        
        if basic_results['csv_files']:
            print(f"  - CSV files: {', '.join(basic_results['csv_files'])}")
        if basic_results['image_files']:
            print(f"  - Image files: {', '.join(basic_results['image_files'])}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
