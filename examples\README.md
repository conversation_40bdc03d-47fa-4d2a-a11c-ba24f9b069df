# IEC PDF Extractor Examples

This directory contains example scripts demonstrating the full capabilities of the IEC PDF Extractor with PyMuPDF integration.

## Examples

### 1. Simple Usage Example (`simple_usage_example.py`)

Demonstrates basic usage with the most common features:
- PDF to Markdown conversion with table and image extraction
- Table-only extraction
- Markdown cleanup and refinement

**Usage:**
```bash
python simple_usage_example.py sample.pdf
```

**What it does:**
- Extracts text, tables, and images from the PDF
- Saves tables as CSV files
- Creates a clean markdown file with IEC section extraction
- Shows basic file organization

### 2. Advanced Extraction Example (`advanced_extraction_example.py`)

Comprehensive demonstration of all PyMuPDF features:
- Multiple text extraction formats (text, dict, json, blocks, words)
- Complete table and image extraction
- Annotation and link processing
- Structured text analysis
- Complete processing pipeline

**Usage:**
```bash
python advanced_extraction_example.py sample.pdf
```

**What it creates:**
```
example_output/
├── basic_extraction.md              # Full extraction with all features
├── final_processed.md               # Complete pipeline result
├── page_001_table_01.csv           # Extracted tables
├── page_001_image_01.png           # Extracted images
├── tables_only/                     # Table-only extraction
│   ├── tables_summary.md
│   └── *.csv
└── format_*/                        # Different text extraction formats
    ├── page_001_text.txt
    ├── page_001_dict.txt
    ├── page_001_json.txt
    └── page_001_*_structured.json
```

## Running the Examples

### Prerequisites

1. Install the package:
```bash
pip install -e .
```

2. Ensure you have a PDF file to test with (preferably an IEC standard document)

### Command Line Examples

You can also use the command line interface directly:

**Basic extraction:**
```bash
iec-pdf-extractor pdf-to-md sample.pdf output.md --verbose
```

**Full feature extraction:**
```bash
iec-pdf-extractor pdf-to-md sample.pdf output.md \
  --extract-tables \
  --extract-images \
  --extract-annotations \
  --extract-metadata \
  --text-format dict \
  --output-dir ./extracted \
  --verbose
```

**Table extraction only:**
```bash
iec-pdf-extractor extract-tables sample.pdf \
  --output-dir ./tables \
  --create-summary \
  --verbose
```

**Complete pipeline:**
```bash
iec-pdf-extractor pipeline sample.pdf final_output.md \
  --extract-tables \
  --extract-images \
  --extract-metadata \
  --fix-toc-formatting \
  --extract-key-sections \
  --verbose
```

## Understanding the Output

### Markdown Files

The generated markdown files include:

1. **Document metadata** (title, author, creation date)
2. **Table of contents** (if available)
3. **Page content** with proper formatting
4. **Table previews** with links to CSV files
5. **Image references** with markdown syntax
6. **Annotations and links** with metadata
7. **Summary sections** for extracted content

### CSV Files

Tables are extracted with the naming convention:
- `page_{page:03d}_table_{index:02d}.csv`
- Each CSV contains the complete table data
- Headers are preserved when detected

### Image Files

Images are saved as:
- `page_{page:03d}_image_{index:02d}.png`
- Original quality is maintained
- CMYK images are converted to RGB

### Structured Text Data

When using advanced text formats:
- `dict` format provides hierarchical text structure
- `json` format includes positioning and font information
- `blocks` format organizes text by paragraphs
- `words` format provides word-level extraction

## Tips for Best Results

1. **Use appropriate text formats:**
   - `text` for simple extraction
   - `dict` for structured analysis
   - `json` for detailed positioning data

2. **Organize output:**
   - Use `--output-dir` to keep extracted files organized
   - Use descriptive output filenames

3. **For IEC standards:**
   - Use `--extract-key-sections` to focus on sections 6-9
   - Use `--fix-toc-formatting` for better TOC handling
   - Use `--fix-internal-headings` for proper section numbering

4. **Performance considerations:**
   - Disable unused features (e.g., `--no-extract-images` if not needed)
   - Use `text` format for faster processing
   - Process large documents in batches if needed

## Troubleshooting

If you encounter issues:

1. **Check PyMuPDF version:** Ensure you have PyMuPDF 1.23.0+ for table extraction
2. **File permissions:** Ensure write access to output directories
3. **Memory usage:** Large PDFs with many images may require more memory
4. **PDF compatibility:** Some PDFs may have protection or unusual formatting

For more help, check the main README.md or open an issue on GitHub.
