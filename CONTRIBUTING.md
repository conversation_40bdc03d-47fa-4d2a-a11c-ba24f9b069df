# Contributing to IEC PDF Extractor

Thank you for considering contributing to the IEC PDF Extracter project! This document provides guidelines and instructions for contributing.

## Development Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/iec-pdf-extractor.git
   cd iec-pdf-extractor
   ```

2. Create a virtual environment and install development dependencies:

   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   pip install -e ".[dev]"
   ```

## Project Structure

The project is organized into two main modules:

- `pdf_to_md`: Converts PDF files to Markdown
- `md_refiner`: Refines and cleans up Markdown files

Common utilities are in the `common` package.

## Testing

Run tests using pytest:

```bash
pytest
```

## Code Style

We use Black for code formatting. Format your code before submitting:

```bash
black src/
```

## Pull Request Process

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Reporting Issues

Please use the GitHub issue tracker to report bugs or suggest features.

## License

By contributing, you agree that your contributions will be licensed under the project's MIT License.
