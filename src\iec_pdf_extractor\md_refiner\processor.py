import re
from ..common.utils import load_markdown, save_markdown, clean_markdown

def process_markdown(input_file, output_file=None, remove_headers=False, remove_footers=False, 
                    remove_page_numbers=False, remove_toc=False, advanced_cleanup=False, 
                    convert_tables=False, fix_toc_formatting=False, fix_toc_tables=False,
                    fix_internal_headings=False, remove_page_markers=False,
                    collapse_blank_lines=False, extract_key_sections=False, verbose=False):
    """
    Processes a Markdown file to clean up structural noise based on provided flags.
    
    Args:
        input_file (str): Path to the input Markdown file.
        output_file (str): Path to save the output. If None, returns the content.
        remove_headers (bool): Flag to remove repeated headers.
        remove_footers (bool): Flag to remove repeated footers.
        remove_page_numbers (bool): Flag to remove page numbers.
        remove_toc (bool): Flag to remove table of contents entries.
        advanced_cleanup (bool): Flag to apply advanced regex-based cleanup.
        convert_tables (bool): Flag to convert text tables to Markdown tables.
        fix_toc_formatting (bool): Flag to fix TOC formatting issues.
        fix_toc_tables (bool): Flag to fix table-like entries in TOC.
        fix_internal_headings (bool): Flag to join section numbers with titles.
        remove_page_markers (bool): Flag to remove page markers.
        collapse_blank_lines (bool): Flag to collapse multiple blank lines.
        extract_key_sections (bool): Flag to extract only sections 6-9 (Scope through Clauses).
        verbose (bool): Flag to enable verbose logging.
    
    Returns:
        str: Cleaned Markdown content if output_file is None, otherwise None.
    """
    def log(message):
        if verbose:
            print(message)

    # Load the input Markdown file
    content = load_markdown(input_file, verbose=verbose)
    
    # Apply standard cleanup operations
    if remove_page_markers:
        content = remove_page_markers_and_artifacts(content, verbose=verbose)
    
    if fix_toc_formatting:
        content = fix_toc_formatting_func(content, verbose=verbose)
        content = fix_subsection_toc_entries(content, verbose=verbose)
    
    if fix_toc_tables:
        content = fix_toc_tables_formatting(content, verbose=verbose)
    
    if fix_internal_headings:
        content = fix_internal_headings_func(content, verbose=verbose)
    
    # Extract key sections (6-9) if requested
    if extract_key_sections:
        content = extract_sections_6_to_9(content, verbose=verbose)
    
    # Process line by line for structural cleanup
    if remove_headers or remove_footers or remove_page_numbers or remove_toc:
        lines = content.split('\n')
        cleaned_lines = []
        
        # Detect TOC section
        in_toc_section = False
        toc_section_start = -1
        toc_section_end = -1
        
        for i, line in enumerate(lines):
            # TOC detection logic
            if line.strip().lower() in ["contents", "table of contents", "toc"]:
                in_toc_section = True
                toc_section_start = i
            
            # End of TOC detection
            if in_toc_section and line.strip() == "" and i > toc_section_start + 5:
                in_toc_section = False
                toc_section_end = i
            
            # Skip line if it's in TOC and we want to remove TOC
            if remove_toc and toc_section_start <= i <= toc_section_end:
                continue
            
            # Skip line if it's a page number and we want to remove page numbers
            if remove_page_numbers and is_page_number(line):
                continue
            
            # Add the line to cleaned content
            cleaned_lines.append(line)
        
        content = '\n'.join(cleaned_lines)
    
    if collapse_blank_lines:
        content = re.sub(r"\n{3,}", "\n\n", content)
        log("Collapsed multiple blank lines.")
    
    if advanced_cleanup:
        log("Applying advanced regex-based cleanup...")
        content = apply_advanced_cleaning(content, verbose=verbose)
    
    if convert_tables:
        log("Converting text tables to Markdown tables...")
        content = convert_text_tables_to_md(content, verbose=verbose)

    content = clean_markdown(content)
    log("Cleanup complete.")
    
    # Save or return the processed content
    if output_file:
        save_markdown(output_file, content, verbose=verbose)
        return None
    return content

def fix_subsection_toc_entries(content, verbose=False):
    """
    Specifically fixes subsection TOC entries where the number and title are on separate lines.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # First, let's directly target the specific pattern we're seeing in the document
    # This pattern is: subsection number on one line, followed by title on next line
    # Example: "3.3.10\nRatings ......................................................................................................... 60"
    
    # Use regex with lookahead to find these patterns
    pattern = r'(\d+\.\d+\.\d+)\s*\n([A-Za-z].*?\.+\s*\d+)'
    content = re.sub(pattern, r'\1 \2', content)
    log("Joined subsection numbers with their titles.")
    
    # Also handle the case where there might be a page marker between them
    pattern = r'(\d+\.\d+\.\d+)\s*\n– \d+ –\s*\n([A-Za-z].*?\.+\s*\d+)'
    content = re.sub(pattern, r'\1 \2', content)
    log("Joined subsection numbers with their titles across page markers.")
    
    # Handle the case with page markers
    pattern = r'(\d+\.\d+\.\d+)\s*\n## Page \d+\s*\n– \d+ –\s*\n([A-Za-z].*?\.+\s*\d+)'
    content = re.sub(pattern, r'\1 \2', content)
    log("Joined subsection numbers with their titles across page markers and numbers.")
    
    return content

def fix_toc_formatting_func(content, verbose=False):
    """
    Fixes TOC formatting issues.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Fix TOC formatting: join lines like "0\nPrinciples ..." into "0 Principles ..."
    content = re.sub(r"(?<=\n)(\d+)\s*\n([^\n]+?)(\.+\s*\d+)", r"\1 \2\3", content)
    log("Fixed TOC formatting for main sections.")
    
    # Fix TOC formatting: join section numbers with titles (e.g., "5.1\nGeneral" -> "5.1 General")
    content = re.sub(r"(?<=\n)(\d+\.\d+)\s*\n([^\n]+?)(\s*\.{2,}\s*\d+)", r"\1 \2\3", content)
    log("Fixed section number formatting in TOC.")
    
    # Fix appendix formatting: join appendix references with titles (e.g., "B.1\nGeneral" -> "B.1 General")
    content = re.sub(r"(?<=\n)([A-Z]\.\d+(?:\.\d+)*)\s*\n([^\n]+?)(\s*\.{2,}\s*\d+)", r"\1 \2\3", content)
    log("Fixed appendix formatting in TOC.")
    
    return content

def fix_toc_tables_formatting(content, verbose=False):
    """
    Fixes table-like entries in TOC.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Find TOC section
    toc_match = re.search(r"(?:Table of Contents|Contents)\s*\n(.*?)\n\n", content, re.DOTALL)
    if not toc_match:
        log("No TOC section found.")
        return content
    
    toc_content = toc_match.group(1)
    
    # Fix table-like entries in TOC
    fixed_toc = re.sub(r"(\d+(?:\.\d+)*)\s+([^\n]+?)\s+(\d+)", r"\1 \2 ............... \3", toc_content)
    
    # Replace original TOC with fixed TOC
    content = content.replace(toc_content, fixed_toc)
    log("Fixed table-like entries in TOC.")
    
    return content

def fix_internal_headings_func(content, verbose=False):
    """
    Fixes internal document headings, focusing only on the Scope section and beyond.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Split content into lines for processing
    lines = content.split('\n')
    
    # First, detect the TOC section to avoid modifying it
    toc_start = toc_end = None
    for i, line in enumerate(lines):
        if line.strip().lower() in ["contents", "table of contents", "toc"]:
            toc_start = i
            continue
        if toc_start is not None and i > toc_start + 1 and line.strip() == "" and i - toc_start > 5:
            toc_end = i
            break
    
    # Now find the Scope section
    scope_start = None
    for i, line in enumerate(lines):
        if "scope" in line.lower() and re.match(r"^\d+\s+Scope", line):
            scope_start = i
            break
    
    # Apply fixes only to content after the Scope section
    if scope_start is not None:
        before_scope = '\n'.join(lines[:scope_start])
        after_scope = '\n'.join(lines[scope_start:])
        
        # Fix section numbers with titles on separate lines
        after_scope = re.sub(r"(?<=\n)(\d+(?:\.\d+)*)\s*\n([A-Z][^\n]*?)(?=\n)", r"\1 \2", after_scope)
        log("Fixed section numbers with titles on separate lines.")
        
        # Fix appendix references with titles on separate lines
        after_scope = re.sub(r"(?<=\n)([A-Z](?:\.\d+)+)\s*\n([A-Z][^\n]*?)(?=\n)", r"\1 \2", after_scope)
        log("Fixed appendix references with titles on separate lines.")
        
        content = before_scope + '\n' + after_scope
    else:
        # Fallback: apply to entire content (should not happen with proper detection)
        log("Warning: Could not identify document structure, applying heading fixes to entire document")
        content = re.sub(r"(?<=\n)(\d+)\s*\n([A-Z][^\n]+)", r"\1 \2", content)
        content = re.sub(r"(?<=\n)(\d+(?:\.\d+)*)\s*\n([A-Z][^\n]*?)(?=\n)", r"\1 \2", content)
        content = re.sub(r"(?<=\n)([A-Z](?:\.\d+)+)\s*\n([A-Z][^\n]*?)(?=\n)", r"\1 \2", content)
    
    log("Fixed internal headings in main content (Scope and beyond).")
    return content

def remove_page_markers_and_artifacts(content, verbose=False):
    """
    Removes page markers and other artifacts from the Markdown content.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Remove page markers (e.g., "## Page 9")
    content = re.sub(r"\n## Page \d+\n", "\n", content)
    log("Removed page markers.")
    
    # Remove page numbers with dashes (e.g., "– 3 –")
    content = re.sub(r"\n– \d+ –\n", "\n", content)
    log("Removed page number lines.")
    
    # Remove specific artifact patterns
    content = re.sub(r"--``,,,`,,`,``,``.*?\n", "", content)
    log("Removed specific artifact patterns.")
    
    # Remove other common PDF artifacts
    content = re.sub(r"\f", "", content)  # Form feed characters
    log("Removed form feed characters.")
    
    # Remove page dividers
    content = re.sub(r"\n---\n", "\n", content)
    log("Removed page dividers.")
    
    return content

def apply_advanced_cleaning(content, verbose=False):
    """
    Applies advanced regex-based cleanup to the Markdown content.
    
    Args:
        content (str): The Markdown content to clean.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The cleaned Markdown content.
    """
    def log(msg):
        if verbose:
            print(msg)

    # 1. Remove specific pattern "--``,,,`,,`,``,``," and similar patterns
    content = re.sub(r"--``\,,,`,,`,``,``.*?\n", "", content)
    log("Removed specific pattern with backticks and commas.")

    # 2. Remove copyright/license block
    content = re.sub(
        r"DS/EN IEC 62368-1:2024\n© Danish Standards Foundation\n(?:.*?\n)*?No reproduction or networking permitted without license from Accuris\n",
        "",
        content
    )
    log("Removed copyright/license.")

    # 3. Collapse multiple blank lines into one
    content = re.sub(r"\n{3,}", "\n\n", content)
    log("Collapsed multiple blank lines.")

    # 4. Trim trailing whitespace
    content = re.sub(r"[ \t]+$", "", content, flags=re.MULTILINE)
    log("Trimmed trailing whitespace.")

    # 5. Fix TOC formatting: join lines like "0\nPrinciples ..." into "0 Principles ..."
    content = re.sub(r"(?<=\n)(\d+)\s*\n([^\n]+?)(\.+\s*\d+)", r"\1 \2\3", content)
    log("Fixed TOC formatting.")

    # 6. Remove page markers from PDF conversion
    content = re.sub(r"---\n## Page \d+\n\n", "", content)
    log("Removed page markers.")

    # 7. Fix broken headings (headings split across lines)
    content = re.sub(r"(#+)\s*\n\s*([^\n]+)", r"\1 \2", content)
    log("Fixed broken headings.")

    # 8. Fix broken list items
    content = re.sub(r"([*-])\s*\n\s*([^\n*-][^\n]*)", r"\1 \2", content)
    log("Fixed broken list items.")

    # 9. Fix section numbers with titles on separate lines (more general pattern)
    content = re.sub(r"(?<=\n)(\d+(?:\.\d+)*)\s*\n([A-Z][^\n]*?)(?=\n)", r"\1 \2", content)
    log("Fixed section numbers with titles on separate lines.")

    return content

def convert_text_tables_to_md(content, verbose=False):
    """
    Converts text-based tables to Markdown tables.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The processed content with text tables converted to Markdown tables.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Identify potential table sections
    # Look for patterns like:
    # +-----+------+
    # | Col1| Col2 |
    # +-----+------+
    
    # Simple table pattern with | characters
    table_pattern = r'(\n\s*\|[^\n]+\|\s*\n\s*\|[^\n]+\|\s*\n)'
    
    def convert_table(match):
        table_text = match.group(1)
        lines = table_text.strip().split('\n')
        
        # Clean up lines
        cleaned_lines = [line.strip() for line in lines]
        
        # Check if this is really a table
        if not all('|' in line for line in cleaned_lines):
            return table_text
        
        # Create
def is_header(line, header_candidates):
    """Detects repeated headers based on frequency."""
    header_candidates[line] = header_candidates.get(line, 0) + 1
    return header_candidates[line] > 1

def is_footer(line, footer_candidates):
    """Detects repeated footers based on frequency."""
    footer_candidates[line] = footer_candidates.get(line, 0) + 1
    return footer_candidates[line] > 1

def is_page_number(line):
    """Detects common page number formats."""
    line = line.strip()
    # Check for standalone page numbers
    if re.match(r"^\d+$", line):
        return True
    # Check for "Page X" format
    if re.match(r"^Page \d+$", line):
        return True
    # Check for "X of Y" format
    if re.match(r"^\d+ of \d+$", line):
        return True
    # Check for page markers from PDF conversion
    if re.match(r"^## Page \d+$", line):
        return True
    return False

def is_toc_entry(line):
    """
    Detects table of contents entries based on common patterns in IEC standards.
    
    Patterns include:
    1. Lines with dotted leaders and trailing numbers (e.g., "Chapter 1..........10")
    2. Lines with just numbers and section titles (e.g., "1.2.3 Section Title")
    3. Indented section numbers followed by titles
    4. Standalone section numbers (e.g., "0.1")
    5. Indented titles that are part of TOC entries
    """
    line = line.strip()
    
    # Skip empty lines
    if not line:
        return False
    
    # Pattern 1: Dotted leaders with page numbers
    if re.match(r".+\.{2,}\s*\d+$", line):
        return True
    
    # Pattern 2: Section numbers (like 1.2.3) followed by text
    if re.match(r"^\d+(\.\d+)*\s+[A-Za-z]", line):
        return True
    
    # Pattern 3: Just numbers (like section numbers in TOC)
    if re.match(r"^\d+(\.\d+)*$", line):
        return True
    
    # Pattern 4: Common TOC headers
    if line.lower() in ["contents", "table of contents", "toc"]:
        return True
    
    # Pattern 5: Indented titles (likely part of TOC)
    if re.match(r"^\s+[A-Za-z]", line) and not re.match(r"^\s+\d+", line):
        return True
    
    # Pattern 6: Appendix entries (A, B, C, etc.)
    if re.match(r"^[A-Z](\.\d+)*\s+[A-Za-z]", line):
        return True
    
    # Pattern 7: Standalone appendix references
    if re.match(r"^[A-Z](\.\d+)*$", line):
        return True
    
    return False

def clean_markdown(content):
    """Removes excessive blank lines from Markdown content."""
    return '\n'.join([line for line in content.split('\n') if line.strip()])

def extract_sections_6_to_9(content, verbose=False):
    """
    Extracts sections 6-9 (Scope, Normative References, Terms and Definitions, Clauses)
    from the IEC standard document.
    
    Args:
        content (str): The Markdown content to process.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        str: The extracted sections.
    """
    def log(msg):
        if verbose:
            print(msg)
    
    # Split content into lines for processing
    lines = content.split('\n')
    
    # Find section 6 (Scope)
    scope_start = None
    for i, line in enumerate(lines):
        if re.match(r'^(?:##?\s*)?6\s+Scope', line, re.IGNORECASE):
            scope_start = i
            break
    
    if scope_start is None:
        log("Warning: Could not find Section 6 (Scope)")
        return content
    
    # Find section 10 (Annexes) or end of document
    section_10_start = None
    for i in range(scope_start + 1, len(lines)):
        if re.match(r'^(?:##?\s*)?10\s+', lines[i], re.IGNORECASE) or re.match(r'^(?:##?\s*)?Annex', lines[i], re.IGNORECASE):
            section_10_start = i
            break
    
    # If section 10 not found, use the end of the document
    if section_10_start is None:
        section_10_start = len(lines)
        log("Note: Could not find Section 10, extracting to end of document")
    
    # Extract sections 6-9
    extracted_content = '\n'.join(lines[scope_start:section_10_start])
    log(f"Extracted sections 6-9 (from line {scope_start} to {section_10_start})")
    
    return extracted_content
