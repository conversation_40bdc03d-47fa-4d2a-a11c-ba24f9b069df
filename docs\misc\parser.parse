def clean_markdown_file(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 1. Remove repeated character string
    content = re.sub(r"--``,,,`,,`,``,``,,```,,,`,`,,-``,,`,,,,-``,,`,,,,---\n?", "", content)

    # 2. Remove copyright/license block
    content = re.sub(
        r"DS/EN IEC 62368-1:2024\n© Danish Standards Foundation\n(?:.*?\n)*?No reproduction or networking permitted without license from Accuris\n",
        "",
        content
    )

    # 3. Collapse multiple blank lines into one
    content = re.sub(r"\n{2,}", "\n\n", content)

    # 4. Trim trailing whitespace
    content = re.sub(r"[ \t]+$", "", content, flags=re.MULTILINE)

    # 5. Fix TOC formatting: join lines like "0\nPrinciples ..." into "0 Principles ..."
    content = re.sub(r"(?<=\n)(\d+)\s*\n([^\n]+?)(\.+\s*\d+)", r"\1 \2\3", content)

    # Save cleaned content
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Cleaned file saved to: {output_path}")

# Example usage
clean_markdown_file("output.md", "output_cleaned.md")