# IEC PDF Extractor

A comprehensive toolchain for converting IEC standard PDF documents into clean, structured Markdown with **full PyMuPDF capabilities**.

## 🚀 Advanced Features

### **Complete PyMuPDF Integration**

- **Automatic Table Detection & CSV Export**: Uses PyMuPDF's `find_tables()` to detect and extract tables with automatic CSV generation
- **Image Extraction**: Extract all images from PDFs with metadata and automatic file saving
- **Annotation & Link Extraction**: Extract comments, annotations, and hyperlinks with full metadata
- **Advanced Text Extraction**: Multiple formats (text, dict, json, xml, html, blocks, words) with positioning data
- **Document Metadata**: Complete document information including TOC, creation date, author, etc.
- **Structured Output Management**: Organized file structure with linked references between markdown and extracted assets

### **Core Capabilities**

- **PDF to Markdown Conversion**: Extract text content from PDF files while preserving structure
- **Advanced Markdown Refinement**: Clean and structure the extracted content
- **Specialized for IEC Standards**: Optimized for the specific format of IEC technical documents
- **Key Section Extraction**: Focus on critical sections (6-9) including Scope, Normative References, Terms and Definitions, and Clauses
- **Flexible Processing Pipeline**: Use individual components or the complete pipeline

### **Table Processing**

- **Automatic Table Detection**: PyMuPDF's advanced table recognition
- **CSV Export**: Each table automatically saved as a separate CSV file
- **Markdown Table Preview**: Tables displayed in markdown with links to full CSV files
- **Table Metadata**: Row/column counts, positioning, and structure information

### **Image Processing**

- **Complete Image Extraction**: All images saved with original quality
- **Multiple Formats**: PNG output with CMYK to RGB conversion when needed
- **Image Metadata**: Dimensions, colorspace, and positioning information
- **Markdown Integration**: Images embedded in markdown with proper references

### **Advanced Text Extraction**

- **Multiple Formats**: Choose from text, dict, json, xml, html, blocks, or words
- **Positioning Data**: Character and word-level positioning information
- **Font Information**: Font names, sizes, and formatting details
- **Structured Data**: Hierarchical text organization with blocks and spans

### **Cleanup Capabilities**

- Extract key sections (6-9) from IEC standards
- Remove headers, footers, and page numbers
- Fix table of contents formatting and structure
- Convert text-based tables to proper Markdown tables
- Join section numbers with their titles
- Remove page markers and PDF artifacts
- Collapse redundant blank lines
- Fix broken headings and list items
- Advanced regex-based cleanup for IEC-specific patterns

## Installation

```bash
pip install iec-pdf-extractor
```

**Requirements:**

- Python 3.10+
- PyMuPDF 1.23.0+ (for table extraction features)
- pandas (optional, for enhanced table processing)

## Usage

### Command Line Interface

The package provides a comprehensive command-line interface with advanced PyMuPDF features:

#### 1. Advanced PDF to Markdown Conversion

**Basic usage:**

```bash
iec-pdf-extractor pdf-to-md input.pdf output.md --verbose
```

**With all advanced features:**

```bash
iec-pdf-extractor pdf-to-md input.pdf output.md \
  --extract-tables \
  --extract-images \
  --extract-annotations \
  --extract-metadata \
  --text-format dict \
  --output-dir ./extracted_content \
  --verbose
```

**Disable specific features:**

```bash
iec-pdf-extractor pdf-to-md input.pdf output.md \
  --no-extract-images \
  --no-extract-annotations \
  --text-format text \
  --verbose
```

#### 2. Table Extraction Only

Extract just tables and save as CSV files:

```bash
iec-pdf-extractor extract-tables input.pdf \
  --output-dir ./tables \
  --create-summary \
  --verbose
```

#### 3. Refine Markdown

```bash
iec-pdf-extractor refine-md input.md output.md --remove-headers --remove-footers --fix-toc-formatting --verbose
```

#### 4. Complete Pipeline (PDF to Clean Markdown)

**Basic pipeline:**

```bash
iec-pdf-extractor pipeline input.pdf output.md --remove-headers --remove-footers --fix-toc-formatting --verbose
```

**Advanced pipeline with full extraction:**

```bash
iec-pdf-extractor pipeline input.pdf output.md \
  --extract-tables \
  --extract-images \
  --extract-metadata \
  --text-format dict \
  --output-dir ./extracted_content \
  --remove-headers \
  --fix-toc-formatting \
  --extract-key-sections \
  --verbose
```

### Available Options

#### PyMuPDF Extraction Options

- `--extract-tables` / `--no-extract-tables`: Enable/disable table extraction and CSV export
- `--extract-images` / `--no-extract-images`: Enable/disable image extraction
- `--extract-annotations` / `--no-extract-annotations`: Enable/disable annotation and link extraction
- `--extract-metadata` / `--no-extract-metadata`: Enable/disable document metadata extraction
- `--text-format`: Choose text extraction format (`text`, `dict`, `json`, `xml`, `html`, `blocks`, `words`)
- `--output-dir`: Directory for extracted files (CSV, images)

#### Markdown Content Control

- `--include-tables-in-md` / `--no-include-tables-in-md`: Include/exclude table references in markdown
- `--include-images-in-md` / `--no-include-images-in-md`: Include/exclude image references in markdown
- `--include-annotations-in-md` / `--no-include-annotations-in-md`: Include/exclude annotations in markdown
- `--include-links-in-md` / `--no-include-links-in-md`: Include/exclude links in markdown
- `--include-metadata-in-md` / `--no-include-metadata-in-md`: Include/exclude metadata in markdown

#### Markdown Refinement Options

- `--remove-headers`: Remove repeated headers
- `--remove-footers`: Remove repeated footers
- `--remove-page-numbers`: Remove page numbers
- `--remove-toc`: Remove table of contents
- `--fix-toc-formatting`: Fix broken TOC lines
- `--fix-toc-tables`: Clean up table-like entries in TOC
- `--fix-internal-headings`: Join section numbers with titles
- `--convert-tables`: Convert text tables to Markdown
- `--remove-page-markers`: Remove page markers
- `--collapse-blank-lines`: Collapse multiple blank lines
- `--advanced-cleanup`: Apply advanced regex-based cleanup
- `--extract-key-sections`: Extract only sections 6-9 (Scope through Clauses)

#### General Options

- `--verbose`: Enable verbose output
- `--create-summary`: Create summary files for extracted content

## Python API

You can also use the package programmatically with full PyMuPDF features:

### Advanced PDF Processing

```python
from iec_pdf_extractor.pdf_to_md import convert_pdf_to_md
from iec_pdf_extractor.md_refiner import refine_markdown

# Advanced PDF to Markdown conversion with all features
results = convert_pdf_to_md(
    input_file="input.pdf",
    output_file="output.md",
    verbose=True,
    extract_tables=True,
    extract_images=True,
    extract_annotations=True,
    extract_metadata=True,
    text_format="dict",  # Get structured text data
    output_dir="./extracted_content",
    include_tables_in_md=True,
    include_images_in_md=True,
    include_annotations_in_md=True,
    include_links_in_md=True,
    include_metadata_in_md=True
)

# Access extraction results
print(f"Processed {results['pages_processed']} pages")
print(f"Found {results['tables_found']} tables")
print(f"Found {results['images_found']} images")
print(f"CSV files: {results['csv_files']}")
print(f"Image files: {results['image_files']}")
```

### Direct PyMuPDF Access

```python
from iec_pdf_extractor.pdf_to_md.parser import parse_pdf

# Get comprehensive PDF data
pdf_data = parse_pdf(
    "input.pdf",
    verbose=True,
    extract_tables=True,
    extract_images=True,
    extract_annotations=True,
    extract_metadata=True,
    text_format="dict",
    output_dir="./extracted"
)

# Access specific data
tables = pdf_data["tables"]
images = pdf_data["images"]
metadata = pdf_data["metadata"]
toc = pdf_data["toc"]
```

### Legacy API (Backward Compatibility)

```python
from iec_pdf_extractor.pdf_to_md import convert_pdf_to_md_legacy
from iec_pdf_extractor.md_refiner import refine_markdown

# Simple conversion (legacy mode)
convert_pdf_to_md_legacy("input.pdf", "intermediate.md", verbose=True)

# Refine Markdown with specific options
refine_markdown(
    input_file="intermediate.md",
    output_file="output.md",
    remove_headers=True,
    fix_toc_formatting=True,
    fix_internal_headings=True,
    convert_tables=True,
    extract_key_sections=True,
    verbose=True
)
```

## Example Workflows

### 1. Complete Document Analysis

Extract everything from an IEC standard PDF with full PyMuPDF features:

```bash
iec-pdf-extractor pipeline /path/to/standards/IEC60601-1.pdf /path/to/output/complete_analysis.md \
  --extract-tables \
  --extract-images \
  --extract-annotations \
  --extract-metadata \
  --text-format dict \
  --output-dir /path/to/output/extracted_content \
  --fix-toc-formatting \
  --extract-key-sections \
  --verbose
```

**Output:**

- `complete_analysis.md` - Comprehensive markdown with all content
- `extracted_content/page_001_table_01.csv` - Extracted tables as CSV
- `extracted_content/page_002_image_01.png` - Extracted images
- Metadata, annotations, and links embedded in markdown

### 2. Table Extraction Only

Extract just tables for data analysis:

```bash
iec-pdf-extractor extract-tables /path/to/standards/IEC60601-1.pdf \
  --output-dir /path/to/tables \
  --create-summary \
  --verbose
```

**Output:**

- Multiple CSV files with table data
- `tables_summary.md` - Overview of all extracted tables

### 3. Legacy Workflow (Backward Compatibility)

Simple text extraction and cleanup:

```bash
# Extract basic text content
iec-pdf-extractor pdf-to-md /path/to/standards/IEC60601-1.pdf /path/to/output/raw_content.md \
  --no-extract-tables \
  --no-extract-images \
  --text-format text

# Clean up and extract key sections
iec-pdf-extractor refine-md /path/to/output/raw_content.md /path/to/output/key_sections.md \
  --extract-key-sections \
  --fix-internal-headings \
  --remove-page-markers
```

### 4. Custom Processing

Fine-tune extraction for specific needs:

```bash
iec-pdf-extractor pdf-to-md input.pdf output.md \
  --extract-tables \
  --no-extract-images \
  --extract-metadata \
  --text-format json \
  --no-include-annotations-in-md \
  --output-dir ./data \
  --verbose
```

**Note:** The tool automatically creates any necessary directories in the output path if they don't exist.

## Output Structure

When using advanced features, the tool creates an organized file structure:

```text
output_directory/
├── document.md                     # Main markdown file
├── page_001_table_01.csv          # Extracted tables
├── page_001_table_02.csv
├── page_002_table_01.csv
├── page_001_image_01.png          # Extracted images
├── page_003_image_01.png
├── page_005_image_02.png
└── tables_summary.md              # Optional summary file
```

### File Naming Conventions

- **Tables**: `page_{page:03d}_table_{index:02d}.csv`
- **Images**: `page_{page:03d}_image_{index:02d}.png`
- **Summary**: `tables_summary.md` (when `--create-summary` is used)

### Markdown Integration

The main markdown file includes:

1. **Document metadata** (title, author, creation date, etc.)
2. **Table of contents** (if available in PDF)
3. **Page content** with embedded references to extracted assets
4. **Table previews** with links to full CSV files
5. **Image references** with proper markdown syntax
6. **Annotations and links** with metadata
7. **Document summaries** listing all extracted content

## PyMuPDF Features Utilized

This tool leverages the full power of PyMuPDF (version 1.23.0+):

- **`page.find_tables()`** - Advanced table detection and extraction
- **`page.get_images()`** - Complete image extraction with metadata
- **`page.get_text(format)`** - Multiple text extraction formats
- **`page.get_links()`** - Hyperlink extraction
- **`page.annots()`** - Annotation and comment extraction
- **`doc.metadata`** - Document metadata access
- **`doc.get_toc()`** - Table of contents extraction
- **Structured text extraction** - Character and word positioning
- **Font information** - Typography and formatting details

## License

This project is licensed under the MIT License - see the LICENSE file for details.
