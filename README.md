# IEC PDF Extractor

A comprehensive toolchain for converting IEC standard PDF documents into clean, structured Markdown.

## Features

- **PDF to Markdown Conversion**: Extract text content from PDF files while preserving structure
- **Advanced Markdown Refinement**: Clean and structure the extracted content
- **Specialized for IEC Standards**: Optimized for the specific format of IEC technical documents
- **Key Section Extraction**: Focus on critical sections (6-9) including Scope, Normative References, Terms and Definitions, and Clauses
- **Flexible Processing Pipeline**: Use individual components or the complete pipeline

### Cleanup Capabilities

- Extract key sections (6-9) from IEC standards
- Remove headers, footers, and page numbers
- Fix table of contents formatting and structure
- Convert text-based tables to proper Markdown tables
- Join section numbers with their titles
- Remove page markers and PDF artifacts
- Collapse redundant blank lines
- Fix broken headings and list items
- Advanced regex-based cleanup for IEC-specific patterns

## Installation

```bash
pip install iec-pdf-extractor
```

## Usage

### Command Line Interface

The package provides a unified command-line interface with three main commands:

#### 1. Convert PDF to Markdown

```bash
iec-pdf-extractor pdf-to-md input.pdf output.md --verbose
```

#### 2. Refine Markdown

```bash
iec-pdf-extractor refine-md input.md output.md --remove-headers --remove-footers --fix-toc-formatting --verbose
```

#### 3. Complete Pipeline (PDF to Clean Markdown)

```bash
iec-pdf-extractor pipeline input.pdf output.md --remove-headers --remove-footers --fix-toc-formatting --verbose
```

### Available Options for Markdown Refinement

- `--remove-headers`: Remove repeated headers
- `--remove-footers`: Remove repeated footers
- `--remove-page-numbers`: Remove page numbers
- `--remove-toc`: Remove table of contents
- `--fix-toc-formatting`: Fix broken TOC lines
- `--fix-toc-tables`: Clean up table-like entries in TOC
- `--fix-internal-headings`: Join section numbers with titles
- `--convert-tables`: Convert text tables to Markdown
- `--remove-page-markers`: Remove page markers
- `--collapse-blank-lines`: Collapse multiple blank lines
- `--advanced-cleanup`: Apply advanced regex-based cleanup
- `--extract-key-sections`: Extract only sections 6-9 (Scope through Clauses)
- `--verbose`: Enable verbose output

## Python API

You can also use the package programmatically:

```python
from iec_pdf_extractor.pdf_to_md import convert_pdf_to_md
from iec_pdf_extractor.md_refiner import refine_markdown

# Convert PDF to Markdown
convert_pdf_to_md("input.pdf", "intermediate.md", verbose=True)

# Refine Markdown with specific options
refine_markdown(
    input_file="intermediate.md",
    output_file="output.md",
    remove_headers=True,
    fix_toc_formatting=True,
    fix_internal_headings=True,
    convert_tables=True,
    verbose=True
)
```

## Example Workflow

1. **Extract content from an IEC standard PDF**:
   ```bash
   iec-pdf-extractor pdf-to-md /path/to/standards/IEC60601-1.pdf /path/to/output/raw_content.md
   ```

2. **Extract and clean up key sections (6-9)**:
   ```bash
   iec-pdf-extractor refine-md /path/to/output/raw_content.md /path/to/output/key_sections.md --extract-key-sections --fix-internal-headings --remove-page-markers
   ```

3. **Or do it all in one step**:
   ```bash
   iec-pdf-extractor pipeline /path/to/standards/IEC60601-1.pdf /path/to/output/key_sections.md --extract-key-sections --fix-internal-headings --remove-page-markers
   ```

Note: The tool automatically creates any necessary directories in the output path if they don't exist.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
