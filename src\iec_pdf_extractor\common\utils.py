"""Common utilities shared between PDF-to-MD and MD-Refiner modules."""

import os
import re

def ensure_directory_exists(file_path):
    """Ensure the directory for the given file path exists."""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        return True
    return False

def load_markdown(file_path, verbose=False):
    """
    Loads the content of a Markdown file.

    Args:
        file_path (str): Path to the Markdown file.
        verbose (bool): If True, prints status messages.

    Returns:
        str: The content of the file as a string.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if verbose:
                print(f"[LOG] Loaded Markdown file: {file_path}")
            return content
    except FileNotFoundError:
        raise FileNotFoundError(f"Markdown file not found: {file_path}")
    except Exception as e:
        raise RuntimeError(f"Error reading Markdown file: {e}")

def save_markdown(file_path, content, verbose=False):
    """
    Saves content to a Markdown file.

    Args:
        file_path (str): Path to save the Markdown file.
        content (str): Content to save.
        verbose (bool): If True, prints status messages.
    """
    try:
        ensure_directory_exists(file_path)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            if verbose:
                print(f"[LOG] Saved Markdown file: {file_path}")
    except Exception as e:
        raise RuntimeError(f"Error saving Markdown file: {e}")

def clean_markdown(content):
    """Removes excessive blank lines from Markdown content."""
    return '\n'.join([line for line in content.split('\n') if line.strip()])

def is_page_number(line):
    """Detects if a line is a page number."""
    line = line.strip()
    return bool(re.match(r'^-?\s*\d+\s*-?$', line) or 
                re.match(r'^Page\s+\d+$', line, re.IGNORECASE) or
                re.match(r'^– \d+ –$', line))
































