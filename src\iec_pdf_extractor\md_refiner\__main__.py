import argparse
import os
import sys
from .processor import process_markdown

def main():
    """
    CLI entry point for the md_refiner tool.
    Parses command-line arguments, processes the Markdown file, and writes the cleaned output.
    """
    parser = argparse.ArgumentParser(description='Clean and refine Markdown files.')
    parser.add_argument('input', type=str, help='Input Markdown file')
    parser.add_argument('output', type=str, help='Output Markdown file')
    parser.add_argument('--remove-headers', action='store_true', help='Remove repeated headers')
    parser.add_argument('--remove-footers', action='store_true', help='Remove repeated footers')
    parser.add_argument('--remove-page-numbers', action='store_true', help='Remove page numbers')
    parser.add_argument('--remove-toc', action='store_true', help='Remove table of contents')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument("--fix-toc-formatting", action="store_true", help="Fix broken TOC lines")
    parser.add_argument("--fix-toc-tables", action="store_true", help="Clean up table-like entries in TOC")
    parser.add_argument("--fix-internal-headings", action="store_true", help="Join section numbers with titles")
    parser.add_argument("--convert-tables", action="store_true", help="Convert text tables to Markdown")
    parser.add_argument("--remove-page-markers", action="store_true", help="Remove page markers")
    parser.add_argument("--collapse-blank-lines", action="store_true", help="Collapse multiple blank lines")
    parser.add_argument("--advanced-cleanup", action="store_true", help="Apply advanced regex-based cleanup")
    parser.add_argument("--extract-key-sections", action="store_true", help="Extract only sections 6-9 (Scope through Clauses)")

    args = parser.parse_args()
    
    # If fix-toc-formatting is enabled, automatically enable remove-page-markers
    # This ensures page markers don't interrupt TOC entries
    if args.fix_toc_formatting:
        args.remove_page_markers = True
    
    try:
        process_markdown(
            input_file=args.input,
            output_file=args.output,
            remove_headers=args.remove_headers,
            remove_footers=args.remove_footers,
            remove_page_numbers=args.remove_page_numbers,
            remove_toc=args.remove_toc,
            advanced_cleanup=args.advanced_cleanup,
            convert_tables=args.convert_tables,
            fix_toc_formatting=args.fix_toc_formatting,
            fix_toc_tables=args.fix_toc_tables,
            fix_internal_headings=args.fix_internal_headings,
            remove_page_markers=args.remove_page_markers,
            collapse_blank_lines=args.collapse_blank_lines,
            extract_key_sections=args.extract_key_sections,
            verbose=args.verbose
        )
        
        if args.verbose:
            print("Markdown refinement complete.")
        sys.exit(0)
    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
