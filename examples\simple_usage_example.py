#!/usr/bin/env python3
"""
Simple Usage Example

This script demonstrates basic usage of the IEC PDF Extractor
with the most common features.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from iec_pdf_extractor.pdf_to_md import convert_pdf_to_md
from iec_pdf_extractor.md_refiner import refine_markdown


def simple_extraction_example(pdf_path: str):
    """Simple PDF to Markdown conversion with table and image extraction."""
    print("Converting PDF to Markdown with table and image extraction...")
    
    # Convert PDF to Markdown with basic features
    results = convert_pdf_to_md(
        input_file=pdf_path,
        output_file="output.md",
        verbose=True,
        extract_tables=True,      # Extract tables as CSV
        extract_images=True,      # Extract images
        extract_metadata=True,    # Include document metadata
        text_format="text",       # Simple text extraction
        output_dir="./extracted", # Directory for CSV and image files
    )
    
    print(f"\nConversion complete!")
    print(f"  - Processed {results['pages_processed']} pages")
    print(f"  - Found {results['tables_found']} tables")
    print(f"  - Found {results['images_found']} images")
    
    if results['csv_files']:
        print(f"  - CSV files: {', '.join(results['csv_files'])}")
    
    return results


def table_extraction_example(pdf_path: str):
    """Extract only tables from PDF."""
    print("\nExtracting tables only...")
    
    from iec_pdf_extractor.pdf_to_md.parser import parse_pdf
    
    # Extract only tables
    pdf_data = parse_pdf(
        pdf_path,
        verbose=True,
        extract_tables=True,
        extract_images=False,
        extract_annotations=False,
        extract_metadata=False,
        output_dir="./tables"
    )
    
    tables = pdf_data.get("tables", [])
    print(f"Found {len(tables)} tables")
    
    # List the CSV files created
    csv_files = [table.get("csv_file") for table in tables if table.get("csv_file")]
    if csv_files:
        print(f"CSV files created: {', '.join(csv_files)}")
    
    return tables


def cleanup_and_refine_example():
    """Clean up and refine the extracted markdown."""
    print("\nCleaning up and refining markdown...")
    
    refine_markdown(
        input_file="output.md",
        output_file="output_clean.md",
        remove_headers=True,
        remove_footers=True,
        fix_toc_formatting=True,
        fix_internal_headings=True,
        remove_page_markers=True,
        collapse_blank_lines=True,
        extract_key_sections=True,  # Extract only IEC sections 6-9
        verbose=True
    )
    
    print("Refinement complete! Check 'output_clean.md'")


def main():
    """Main function demonstrating simple usage."""
    if len(sys.argv) != 2:
        print("Usage: python simple_usage_example.py <pdf_file>")
        print("\nThis script demonstrates simple usage of the IEC PDF Extractor.")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    print(f"Processing PDF: {pdf_path}")
    print("=" * 50)
    
    try:
        # Example 1: Simple extraction with tables and images
        results = simple_extraction_example(pdf_path)
        
        # Example 2: Table-only extraction
        tables = table_extraction_example(pdf_path)
        
        # Example 3: Clean up and refine
        cleanup_and_refine_example()
        
        print("\n" + "=" * 50)
        print("EXAMPLES COMPLETE")
        print("=" * 50)
        print("\nFiles created:")
        print("  - output.md (raw extraction)")
        print("  - output_clean.md (cleaned and refined)")
        print("  - extracted/ (tables and images)")
        print("  - tables/ (tables only)")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
