import re
import os

def load_markdown(file_path, verbose=False):
    """
    Loads the content of a Markdown file.

    Args:
        file_path (str): Path to the Markdown file.
        verbose (bool): If True, prints status messages.

    Returns:
        str: The content of the file as a string.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not file_path.lower().endswith('.md'):
                raise ValueError("Only Markdown (.md) files are supported.")
            if verbose:
                print(f"[LOG] Loaded Markdown file: {file_path}")
            return content
    except FileNotFoundError:
        raise FileNotFoundError(f"Markdown file not found: {file_path}")
    except Exception as e:
        raise RuntimeError(f"Error reading Markdown file: {e}")
    

def save_markdown(output_path, content, verbose=False):
    """
    Saves the cleaned Markdown content to a file.

    Args:
        output_path (str): Path to save the Markdown file.
        content (str): The Markdown content to write.
        verbose (bool): If True, prints status messages.
    """
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)
    if verbose:
        print(f"[LOG] Saved cleaned Markdown to: {output_path}")
