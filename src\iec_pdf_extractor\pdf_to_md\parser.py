"""
PDF parser module for extracting text content from PDF files.
"""

import fitz  # PyMuPDF

def parse_pdf(pdf_path, verbose=False):
    """
    Parse a PDF file and extract text content.
    
    Args:
        pdf_path (str): Path to the PDF file.
        verbose (bool): Whether to print verbose output.
        
    Returns:
        list: List of dictionaries containing page content and metadata.
    """
    if verbose:
        print(f"Opening PDF: {pdf_path}")
    
    try:
        doc = fitz.open(pdf_path)
    except Exception as e:
        raise RuntimeError(f"Error opening PDF file: {e}")
    
    pages = []
    
    for i, page in enumerate(doc):
        if verbose and (i == 0 or (i+1) % 10 == 0):
            print(f"Processing page {i+1}/{len(doc)}")
        
        # Extract text
        text = page.get_text()
        
        # Extract page dimensions
        width, height = page.rect.width, page.rect.height
        
        # Store page data
        page_data = {
            "number": i + 1,
            "text": text,
            "width": width,
            "height": height
        }
        
        pages.append(page_data)
    
    if verbose:
        print(f"Extracted {len(pages)} pages from PDF")
    
    doc.close()
    return pages
