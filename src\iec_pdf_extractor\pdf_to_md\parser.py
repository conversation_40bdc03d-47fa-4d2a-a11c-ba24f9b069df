"""
PDF parser module for extracting comprehensive content from PDF files using PyMuPDF.
"""

import fitz  # PyMuPDF
import os
import json
import csv
from typing import Dict, List, Any, Optional, Tuple

def parse_pdf(pdf_path: str, verbose: bool = False, extract_tables: bool = True,
              extract_images: bool = True, extract_annotations: bool = True,
              extract_metadata: bool = True, text_format: str = "text",
              output_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    Parse a PDF file and extract comprehensive content using PyMuPDF's advanced features.

    Args:
        pdf_path (str): Path to the PDF file.
        verbose (bool): Whether to print verbose output.
        extract_tables (bool): Whether to extract tables and save as CSV.
        extract_images (bool): Whether to extract images.
        extract_annotations (bool): Whether to extract annotations and links.
        extract_metadata (bool): Whether to extract document metadata.
        text_format (str): Text extraction format ('text', 'dict', 'json', 'xml', 'html', 'blocks', 'words').
        output_dir (str): Directory to save extracted files (images, CSVs). If None, uses PDF directory.

    Returns:
        dict: Comprehensive document data including pages, tables, images, annotations, and metadata.
    """
    if verbose:
        print(f"Opening PDF: {pdf_path}")

    try:
        doc = fitz.open(pdf_path)
    except Exception as e:
        raise RuntimeError(f"Error opening PDF file: {e}")

    # Setup output directory
    if output_dir is None:
        output_dir = os.path.dirname(pdf_path) or "."
    os.makedirs(output_dir, exist_ok=True)

    # Initialize result structure
    result = {
        "document_info": {
            "path": pdf_path,
            "page_count": len(doc),
            "output_dir": output_dir
        },
        "pages": [],
        "tables": [],
        "images": [],
        "annotations": [],
        "links": [],
        "metadata": {},
        "toc": []
    }

    # Extract document-level metadata
    if extract_metadata:
        result["metadata"] = _extract_document_metadata(doc, verbose)
        result["toc"] = _extract_table_of_contents(doc, verbose)

    # Process each page
    for i, page in enumerate(doc):
        if verbose and (i == 0 or (i+1) % 10 == 0):
            print(f"Processing page {i+1}/{len(doc)}")

        page_data = _extract_page_content(
            page, i + 1, text_format, extract_tables, extract_images,
            extract_annotations, output_dir, verbose
        )

        result["pages"].append(page_data)

        # Collect page-level data into document-level collections
        if page_data.get("tables"):
            result["tables"].extend(page_data["tables"])
        if page_data.get("images"):
            result["images"].extend(page_data["images"])
        if page_data.get("annotations"):
            result["annotations"].extend(page_data["annotations"])
        if page_data.get("links"):
            result["links"].extend(page_data["links"])

    if verbose:
        print(f"Extraction complete:")
        print(f"  - {len(result['pages'])} pages processed")
        print(f"  - {len(result['tables'])} tables found")
        print(f"  - {len(result['images'])} images extracted")
        print(f"  - {len(result['annotations'])} annotations found")
        print(f"  - {len(result['links'])} links found")

    doc.close()
    return result


def _extract_document_metadata(doc: fitz.Document, verbose: bool = False) -> Dict[str, Any]:
    """Extract document-level metadata."""
    if verbose:
        print("Extracting document metadata...")

    metadata = {}

    # Standard metadata
    doc_metadata = doc.metadata
    if doc_metadata:
        metadata.update(doc_metadata)

    # Additional document properties
    metadata.update({
        "page_count": len(doc),
        "is_pdf": doc.is_pdf,
        "is_encrypted": doc.is_encrypted,
        "needs_pass": doc.needs_pass,
        "permissions": doc.permissions if hasattr(doc, 'permissions') else None,
        "page_mode": getattr(doc, 'page_mode', None),
        "page_layout": getattr(doc, 'page_layout', None)
    })

    return metadata


def _extract_table_of_contents(doc: fitz.Document, verbose: bool = False) -> List[Dict[str, Any]]:
    """Extract table of contents."""
    if verbose:
        print("Extracting table of contents...")

    try:
        toc = doc.get_toc()
        toc_list = []

        for entry in toc:
            if len(entry) >= 3:
                toc_entry = {
                    "level": entry[0],
                    "title": entry[1],
                    "page": entry[2],
                    "dest": entry[3] if len(entry) > 3 else None
                }
                toc_list.append(toc_entry)

        return toc_list
    except Exception as e:
        if verbose:
            print(f"Warning: Could not extract TOC: {e}")
        return []


def _extract_page_content(page: fitz.Page, page_num: int, text_format: str,
                         extract_tables: bool, extract_images: bool,
                         extract_annotations: bool, output_dir: str,
                         verbose: bool = False) -> Dict[str, Any]:
    """Extract comprehensive content from a single page."""
    page_data = {
        "number": page_num,
        "width": page.rect.width,
        "height": page.rect.height,
        "rotation": page.rotation,
        "text": "",
        "structured_text": None,
        "tables": [],
        "images": [],
        "annotations": [],
        "links": []
    }

    # Extract text in specified format
    page_data["text"] = page.get_text(text_format)

    # For structured formats, also store the structured data
    if text_format in ["dict", "json", "xml", "html", "blocks", "words"]:
        try:
            if text_format == "dict":
                page_data["structured_text"] = page.get_text("dict")
            elif text_format == "json":
                page_data["structured_text"] = json.loads(page.get_text("json"))
            elif text_format in ["blocks", "words"]:
                page_data["structured_text"] = page.get_text(text_format)
        except Exception as e:
            if verbose:
                print(f"Warning: Could not extract structured text for page {page_num}: {e}")

    # Extract tables
    if extract_tables:
        page_data["tables"] = _extract_page_tables(page, page_num, output_dir, verbose)

    # Extract images
    if extract_images:
        page_data["images"] = _extract_page_images(page, page_num, output_dir, verbose)

    # Extract annotations and links
    if extract_annotations:
        page_data["annotations"] = _extract_page_annotations(page, page_num, verbose)
        page_data["links"] = _extract_page_links(page, page_num, verbose)

    return page_data


def _extract_page_tables(page: fitz.Page, page_num: int, output_dir: str, verbose: bool = False) -> List[Dict[str, Any]]:
    """Extract tables from a page and save as CSV files."""
    tables_data = []

    try:
        # Find tables on the page
        tables = page.find_tables()

        if verbose and len(tables.tables) > 0:
            print(f"Found {len(tables.tables)} table(s) on page {page_num}")

        for i, table in enumerate(tables.tables):
            table_info = {
                "page": page_num,
                "table_index": i,
                "bbox": tuple(table.bbox),
                "row_count": table.row_count,
                "col_count": table.col_count,
                "csv_file": None,
                "data": []
            }

            try:
                # Extract table data
                table_data = table.extract()
                table_info["data"] = table_data

                # Save as CSV
                csv_filename = f"page_{page_num:03d}_table_{i:02d}.csv"
                csv_path = os.path.join(output_dir, csv_filename)

                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(table_data)

                table_info["csv_file"] = csv_filename

                if verbose:
                    print(f"Saved table {i} from page {page_num} to {csv_filename}")

            except Exception as e:
                if verbose:
                    print(f"Warning: Could not extract table {i} from page {page_num}: {e}")

            tables_data.append(table_info)

    except Exception as e:
        if verbose:
            print(f"Warning: Could not find tables on page {page_num}: {e}")

    return tables_data


def _extract_page_images(page: fitz.Page, page_num: int, output_dir: str, verbose: bool = False) -> List[Dict[str, Any]]:
    """Extract images from a page."""
    images_data = []

    try:
        image_list = page.get_images()

        if verbose and len(image_list) > 0:
            print(f"Found {len(image_list)} image(s) on page {page_num}")

        for i, img in enumerate(image_list):
            try:
                # Get image info
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)

                image_info = {
                    "page": page_num,
                    "image_index": i,
                    "xref": xref,
                    "width": pix.width,
                    "height": pix.height,
                    "colorspace": pix.colorspace.name if pix.colorspace else "unknown",
                    "alpha": pix.alpha,
                    "image_file": None
                }

                # Save image
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_filename = f"page_{page_num:03d}_image_{i:02d}.png"
                    img_path = os.path.join(output_dir, img_filename)
                    pix.save(img_path)
                    image_info["image_file"] = img_filename

                    if verbose:
                        print(f"Saved image {i} from page {page_num} to {img_filename}")
                else:  # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    img_filename = f"page_{page_num:03d}_image_{i:02d}.png"
                    img_path = os.path.join(output_dir, img_filename)
                    pix1.save(img_path)
                    image_info["image_file"] = img_filename
                    pix1 = None

                    if verbose:
                        print(f"Saved converted image {i} from page {page_num} to {img_filename}")

                pix = None
                images_data.append(image_info)

            except Exception as e:
                if verbose:
                    print(f"Warning: Could not extract image {i} from page {page_num}: {e}")

    except Exception as e:
        if verbose:
            print(f"Warning: Could not get images from page {page_num}: {e}")

    return images_data


def _extract_page_annotations(page: fitz.Page, page_num: int, verbose: bool = False) -> List[Dict[str, Any]]:
    """Extract annotations from a page."""
    annotations_data = []

    try:
        for annot in page.annots():
            try:
                annot_info = {
                    "page": page_num,
                    "type": annot.type[1],  # Get annotation type name
                    "content": annot.info.get("content", ""),
                    "author": annot.info.get("title", ""),
                    "subject": annot.info.get("subject", ""),
                    "bbox": tuple(annot.rect),
                    "flags": annot.flags,
                    "opacity": getattr(annot, 'opacity', None)
                }

                # Get additional properties if available
                if hasattr(annot, 'popup'):
                    annot_info["has_popup"] = annot.popup is not None

                annotations_data.append(annot_info)

            except Exception as e:
                if verbose:
                    print(f"Warning: Could not extract annotation from page {page_num}: {e}")

    except Exception as e:
        if verbose:
            print(f"Warning: Could not get annotations from page {page_num}: {e}")

    if verbose and len(annotations_data) > 0:
        print(f"Found {len(annotations_data)} annotation(s) on page {page_num}")

    return annotations_data


def _extract_page_links(page: fitz.Page, page_num: int, verbose: bool = False) -> List[Dict[str, Any]]:
    """Extract links from a page."""
    links_data = []

    try:
        links = page.get_links()

        for link in links:
            link_info = {
                "page": page_num,
                "bbox": tuple(link.get("from", [])),
                "kind": link.get("kind", 0),
                "page_dest": link.get("page", None),
                "to": link.get("to", None),
                "uri": link.get("uri", ""),
                "file": link.get("file", ""),
                "zoom": link.get("zoom", 0)
            }

            # Determine link type
            if link_info["kind"] == fitz.LINK_URI:
                link_info["type"] = "external"
            elif link_info["kind"] == fitz.LINK_GOTO:
                link_info["type"] = "internal"
            elif link_info["kind"] == fitz.LINK_GOTOR:
                link_info["type"] = "remote"
            else:
                link_info["type"] = "other"

            links_data.append(link_info)

    except Exception as e:
        if verbose:
            print(f"Warning: Could not get links from page {page_num}: {e}")

    if verbose and len(links_data) > 0:
        print(f"Found {len(links_data)} link(s) on page {page_num}")

    return links_data


# Legacy function for backward compatibility
def parse_pdf_legacy(pdf_path: str, verbose: bool = False) -> List[Dict[str, Any]]:
    """
    Legacy function that maintains the original API for backward compatibility.

    Args:
        pdf_path (str): Path to the PDF file.
        verbose (bool): Whether to print verbose output.

    Returns:
        list: List of dictionaries containing page content and metadata (original format).
    """
    result = parse_pdf(pdf_path, verbose=verbose, extract_tables=False,
                      extract_images=False, extract_annotations=False,
                      extract_metadata=False, text_format="text")

    # Convert to legacy format
    legacy_pages = []
    for page_data in result["pages"]:
        legacy_page = {
            "number": page_data["number"],
            "text": page_data["text"],
            "width": page_data["width"],
            "height": page_data["height"]
        }
        legacy_pages.append(legacy_page)

    return legacy_pages
