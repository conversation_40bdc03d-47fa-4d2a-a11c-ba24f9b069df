import argparse
import sys
from .parser import parse_pdf
from .converter import convert_to_markdown

def main():
    """Main entry point for the PDF to Markdown converter."""
    parser = argparse.ArgumentParser(description="Convert PDF to Markdown")
    parser.add_argument("input", help="Input PDF file")
    parser.add_argument("output", help="Output Markdown file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    try:
        if args.verbose:
            print(f"Parsing PDF: {args.input}")
        
        # Pass the verbose flag to parse_pdf
        pages = parse_pdf(args.input, verbose=args.verbose)
        
        if args.verbose:
            print("Converting to Markdown...")
        
        markdown = convert_to_markdown(pages)
        
        if args.verbose:
            print(f"Saving Markdown to: {args.output}")
        
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(markdown)
        
        if args.verbose:
            print("Conversion complete.")
    
    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
