import argparse
import sys
from .parser import parse_pdf_legacy
from .converter import convert_to_markdown

def main():
    """Main entry point for the PDF to Markdown converter (legacy mode for backward compatibility)."""
    parser = argparse.ArgumentParser(description="Convert PDF to Markdown (Legacy Mode)")
    parser.add_argument("input", help="Input PDF file")
    parser.add_argument("output", help="Output Markdown file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    try:
        if args.verbose:
            print(f"Parsing PDF (legacy mode): {args.input}")

        # Use legacy parser for backward compatibility
        pages = parse_pdf_legacy(args.input, verbose=args.verbose)

        if args.verbose:
            print("Converting to Markdown...")

        markdown = convert_to_markdown(pages, verbose=args.verbose)

        if args.verbose:
            print(f"Saving Markdown to: {args.output}")

        with open(args.output, "w", encoding="utf-8") as f:
            f.write(markdown)

        if args.verbose:
            print("Legacy conversion complete.")
            print("Note: For advanced features (table extraction, image extraction, etc.), use the main CLI: iec-pdf-extractor")

    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
