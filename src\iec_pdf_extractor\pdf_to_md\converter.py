"""
Converter module for transforming PDF content to Markdown.
"""

import re

def convert_to_markdown(pages, verbose=False):
    """
    Convert parsed PDF pages to Markdown format.
    
    Args:
        pages (list): List of dictionaries containing page content and metadata.
        verbose (bool): Enable verbose output for debugging.
    
    Returns:
        str: Combined Markdown-formatted string with page labels.
    """
    # Check for empty input
    if not pages:
        return ""
    
    markdown_content = ""
    
    # Combine each page's content with spacing between pages
    for page in pages:
        page_num = page["number"]
        page_text = page["text"].strip()
        
        # Add page marker and content
        markdown_content += f"---\n## Page {page_num}\n\n{page_text}\n\n"
    
    if verbose:
        print(f"Converted {len(pages)} pages to Markdown format")
    
    return markdown_content
