"""
Converter module for transforming comprehensive PDF content to Markdown.
"""

import re
import os
from typing import Dict, List, Any, Union

def convert_to_markdown(data: Union[List[Dict], Dict[str, Any]], verbose: bool = False,
                       include_tables: bool = True, include_images: bool = True,
                       include_annotations: bool = True, include_links: bool = True,
                       include_metadata: bool = True) -> str:
    """
    Convert comprehensive PDF data to Markdown format.

    Args:
        data: Either legacy list of page dictionaries or new comprehensive data structure.
        verbose (bool): Enable verbose output for debugging.
        include_tables (bool): Include table references in markdown.
        include_images (bool): Include image references in markdown.
        include_annotations (bool): Include annotations in markdown.
        include_links (bool): Include links in markdown.
        include_metadata (bool): Include document metadata in markdown.

    Returns:
        str: Combined Markdown-formatted string with comprehensive content.
    """
    # Handle legacy format (list of pages)
    if isinstance(data, list):
        return _convert_legacy_format(data, verbose)

    # Handle new comprehensive format
    if not isinstance(data, dict) or "pages" not in data:
        if verbose:
            print("Warning: Invalid data format, returning empty string")
        return ""

    markdown_content = ""

    # Add document metadata
    if include_metadata and data.get("metadata"):
        markdown_content += _generate_metadata_section(data["metadata"], verbose)

    # Add table of contents
    if include_metadata and data.get("toc"):
        markdown_content += _generate_toc_section(data["toc"], verbose)

    # Process pages
    pages = data.get("pages", [])
    if not pages:
        return markdown_content

    for page in pages:
        page_content = _convert_page_to_markdown(
            page, include_tables, include_images, include_annotations, include_links, verbose
        )
        markdown_content += page_content

    # Add document-level summaries
    if include_tables and data.get("tables"):
        markdown_content += _generate_tables_summary(data["tables"], verbose)

    if include_images and data.get("images"):
        markdown_content += _generate_images_summary(data["images"], verbose)

    if verbose:
        print(f"Converted {len(pages)} pages to comprehensive Markdown format")

    return markdown_content


def _convert_legacy_format(pages: List[Dict], verbose: bool = False) -> str:
    """Convert legacy page format to markdown (backward compatibility)."""
    if not pages:
        return ""

    markdown_content = ""

    for page in pages:
        page_num = page["number"]
        page_text = page["text"].strip()

        # Add page marker and content
        markdown_content += f"---\n## Page {page_num}\n\n{page_text}\n\n"

    if verbose:
        print(f"Converted {len(pages)} pages to Markdown format (legacy mode)")

    return markdown_content


def _generate_metadata_section(metadata: Dict[str, Any], verbose: bool = False) -> str:
    """Generate markdown section for document metadata."""
    if not metadata:
        return ""

    content = "# Document Information\n\n"

    # Key metadata fields
    key_fields = {
        "title": "Title",
        "author": "Author",
        "subject": "Subject",
        "creator": "Creator",
        "producer": "Producer",
        "creationDate": "Creation Date",
        "modDate": "Modification Date",
        "format": "Format",
        "page_count": "Total Pages"
    }

    for key, label in key_fields.items():
        if key in metadata and metadata[key]:
            content += f"**{label}:** {metadata[key]}\n\n"

    # Additional properties
    if metadata.get("is_encrypted"):
        content += "**Security:** Document is encrypted\n\n"

    content += "---\n\n"

    if verbose:
        print("Added document metadata section")

    return content


def _generate_toc_section(toc: List[Dict[str, Any]], verbose: bool = False) -> str:
    """Generate markdown section for table of contents."""
    if not toc:
        return ""

    content = "# Table of Contents\n\n"

    for entry in toc:
        level = entry.get("level", 1)
        title = entry.get("title", "")
        page = entry.get("page", "")

        # Create indentation based on level
        indent = "  " * (level - 1)
        content += f"{indent}- {title}"

        if page:
            content += f" (Page {page})"

        content += "\n"

    content += "\n---\n\n"

    if verbose:
        print(f"Added table of contents with {len(toc)} entries")

    return content


def _convert_page_to_markdown(page: Dict[str, Any], include_tables: bool, include_images: bool,
                             include_annotations: bool, include_links: bool, verbose: bool = False) -> str:
    """Convert a single page to markdown format."""
    page_num = page["number"]
    page_text = page.get("text", "").strip()

    content = f"---\n## Page {page_num}\n\n"

    # Add main text content
    if page_text:
        content += f"{page_text}\n\n"

    # Add tables section
    if include_tables and page.get("tables"):
        content += _generate_page_tables_section(page["tables"], page_num, verbose)

    # Add images section
    if include_images and page.get("images"):
        content += _generate_page_images_section(page["images"], page_num, verbose)

    # Add annotations section
    if include_annotations and page.get("annotations"):
        content += _generate_page_annotations_section(page["annotations"], page_num, verbose)

    # Add links section
    if include_links and page.get("links"):
        content += _generate_page_links_section(page["links"], page_num, verbose)

    return content


def _generate_page_tables_section(tables: List[Dict[str, Any]], page_num: int, verbose: bool = False) -> str:
    """Generate markdown section for page tables."""
    if not tables:
        return ""

    content = f"### Tables on Page {page_num}\n\n"

    for i, table in enumerate(tables):
        content += f"**Table {i + 1}:**\n"
        content += f"- Dimensions: {table.get('row_count', 'N/A')} rows × {table.get('col_count', 'N/A')} columns\n"

        if table.get("csv_file"):
            content += f"- CSV Export: [{table['csv_file']}]({table['csv_file']})\n"

        # Show first few rows as preview
        if table.get("data") and len(table["data"]) > 0:
            content += "- Preview:\n\n"

            # Create markdown table from first few rows
            preview_rows = table["data"][:5]  # Show first 5 rows
            if preview_rows:
                # Header row
                if len(preview_rows) > 0:
                    header = preview_rows[0]
                    content += "| " + " | ".join(str(cell) for cell in header) + " |\n"
                    content += "| " + " | ".join("---" for _ in header) + " |\n"

                    # Data rows
                    for row in preview_rows[1:]:
                        content += "| " + " | ".join(str(cell) for cell in row) + " |\n"

                if len(table["data"]) > 5:
                    content += f"\n*({len(table['data']) - 5} more rows in CSV file)*\n"

            content += "\n"

        content += "\n"

    return content


def _generate_page_images_section(images: List[Dict[str, Any]], page_num: int, verbose: bool = False) -> str:
    """Generate markdown section for page images."""
    if not images:
        return ""

    content = f"### Images on Page {page_num}\n\n"

    for i, image in enumerate(images):
        content += f"**Image {i + 1}:**\n"
        content += f"- Dimensions: {image.get('width', 'N/A')} × {image.get('height', 'N/A')} pixels\n"
        content += f"- Colorspace: {image.get('colorspace', 'N/A')}\n"

        if image.get("image_file"):
            content += f"- File: ![Image {i + 1}]({image['image_file']})\n"

        content += "\n"

    return content


def _generate_page_annotations_section(annotations: List[Dict[str, Any]], page_num: int, verbose: bool = False) -> str:
    """Generate markdown section for page annotations."""
    if not annotations:
        return ""

    content = f"### Annotations on Page {page_num}\n\n"

    for i, annotation in enumerate(annotations):
        content += f"**Annotation {i + 1}** ({annotation.get('type', 'Unknown')}):\n"

        if annotation.get("content"):
            content += f"- Content: {annotation['content']}\n"

        if annotation.get("author"):
            content += f"- Author: {annotation['author']}\n"

        if annotation.get("subject"):
            content += f"- Subject: {annotation['subject']}\n"

        content += "\n"

    return content


def _generate_page_links_section(links: List[Dict[str, Any]], page_num: int, verbose: bool = False) -> str:
    """Generate markdown section for page links."""
    if not links:
        return ""

    content = f"### Links on Page {page_num}\n\n"

    for i, link in enumerate(links):
        link_type = link.get("type", "unknown")
        content += f"**Link {i + 1}** ({link_type}):\n"

        if link.get("uri"):
            content += f"- URL: [{link['uri']}]({link['uri']})\n"
        elif link.get("page_dest"):
            content += f"- Destination: Page {link['page_dest']}\n"
        elif link.get("file"):
            content += f"- File: {link['file']}\n"

        content += "\n"

    return content


def _generate_tables_summary(tables: List[Dict[str, Any]], verbose: bool = False) -> str:
    """Generate document-level tables summary."""
    if not tables:
        return ""

    content = "# Document Tables Summary\n\n"
    content += f"Total tables found: {len(tables)}\n\n"

    # Group by page
    pages_with_tables = {}
    for table in tables:
        page = table.get("page", 0)
        if page not in pages_with_tables:
            pages_with_tables[page] = []
        pages_with_tables[page].append(table)

    for page in sorted(pages_with_tables.keys()):
        page_tables = pages_with_tables[page]
        content += f"## Page {page} ({len(page_tables)} table{'s' if len(page_tables) != 1 else ''})\n\n"

        for i, table in enumerate(page_tables):
            if table.get("csv_file"):
                content += f"- Table {i + 1}: [{table['csv_file']}]({table['csv_file']}) "
                content += f"({table.get('row_count', 'N/A')} × {table.get('col_count', 'N/A')})\n"

        content += "\n"

    content += "---\n\n"
    return content


def _generate_images_summary(images: List[Dict[str, Any]], verbose: bool = False) -> str:
    """Generate document-level images summary."""
    if not images:
        return ""

    content = "# Document Images Summary\n\n"
    content += f"Total images found: {len(images)}\n\n"

    # Group by page
    pages_with_images = {}
    for image in images:
        page = image.get("page", 0)
        if page not in pages_with_images:
            pages_with_images[page] = []
        pages_with_images[page].append(image)

    for page in sorted(pages_with_images.keys()):
        page_images = pages_with_images[page]
        content += f"## Page {page} ({len(page_images)} image{'s' if len(page_images) != 1 else ''})\n\n"

        for i, image in enumerate(page_images):
            if image.get("image_file"):
                content += f"- Image {i + 1}: [{image['image_file']}]({image['image_file']}) "
                content += f"({image.get('width', 'N/A')} × {image.get('height', 'N/A')})\n"

        content += "\n"

    content += "---\n\n"
    return content
