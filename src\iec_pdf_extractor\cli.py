"""
Unified CLI interface for the IEC PDF Extractor toolchain.
"""

import argparse
import os
import sys
import tempfile
from .pdf_to_md import convert_pdf_to_md
from .md_refiner import refine_markdown

def main():
    """Main entry point for the IEC PDF Extractor CLI."""
    parser = argparse.ArgumentParser(
        description="IEC PDF Extractor - Convert PDF files to clean Markdown"
    )
    
    # Create subparsers for each command
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # PDF to MD command
    pdf_parser = subparsers.add_parser("pdf-to-md", help="Convert PDF to Markdown")
    pdf_parser.add_argument("input", help="Input PDF file")
    pdf_parser.add_argument("output", help="Output Markdown file")
    pdf_parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    # MD Refiner command
    md_parser = subparsers.add_parser("refine-md", help="Refine Markdown files")
    md_parser.add_argument("input", help="Input Markdown file")
    md_parser.add_argument("output", help="Output Markdown file")
    md_parser.add_argument("--remove-headers", action="store_true", help="Remove repeated headers")
    md_parser.add_argument("--remove-footers", action="store_true", help="Remove repeated footers")
    md_parser.add_argument("--remove-page-numbers", action="store_true", help="Remove page numbers")
    md_parser.add_argument("--remove-toc", action="store_true", help="Remove table of contents")
    md_parser.add_argument("--fix-toc-formatting", action="store_true", help="Fix broken TOC lines")
    md_parser.add_argument("--fix-toc-tables", action="store_true", help="Clean up table-like entries in TOC")
    md_parser.add_argument("--fix-internal-headings", action="store_true", help="Join section numbers with titles")
    md_parser.add_argument("--convert-tables", action="store_true", help="Convert text tables to Markdown")
    md_parser.add_argument("--remove-page-markers", action="store_true", help="Remove page markers")
    md_parser.add_argument("--collapse-blank-lines", action="store_true", help="Collapse multiple blank lines")
    md_parser.add_argument("--advanced-cleanup", action="store_true", help="Apply advanced regex-based cleanup")
    md_parser.add_argument("--extract-key-sections", action="store_true", help="Extract only sections 6-9 (Scope through Clauses)")
    md_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    # Pipeline command (convert PDF and then refine)
    pipeline_parser = subparsers.add_parser("pipeline", help="Convert PDF to Markdown and then refine it")
    pipeline_parser.add_argument("input", help="Input PDF file")
    pipeline_parser.add_argument("output", help="Output Markdown file")
    pipeline_parser.add_argument("--temp-file", help="Temporary file for intermediate Markdown")
    pipeline_parser.add_argument("--remove-headers", action="store_true", help="Remove repeated headers")
    pipeline_parser.add_argument("--remove-footers", action="store_true", help="Remove repeated footers")
    pipeline_parser.add_argument("--remove-page-numbers", action="store_true", help="Remove page numbers")
    pipeline_parser.add_argument("--remove-toc", action="store_true", help="Remove table of contents")
    pipeline_parser.add_argument("--fix-toc-formatting", action="store_true", help="Fix broken TOC lines")
    pipeline_parser.add_argument("--fix-toc-tables", action="store_true", help="Clean up table-like entries in TOC")
    pipeline_parser.add_argument("--fix-internal-headings", action="store_true", help="Join section numbers with titles")
    pipeline_parser.add_argument("--convert-tables", action="store_true", help="Convert text tables to Markdown")
    pipeline_parser.add_argument("--remove-page-markers", action="store_true", help="Remove page markers")
    pipeline_parser.add_argument("--collapse-blank-lines", action="store_true", help="Collapse multiple blank lines")
    pipeline_parser.add_argument("--advanced-cleanup", action="store_true", help="Apply advanced regex-based cleanup")
    pipeline_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        sys.exit(1)
    
    try:
        if args.command == "pdf-to-md":
            convert_pdf_to_md(args.input, args.output, verbose=args.verbose)
            if args.verbose:
                print("PDF to Markdown conversion complete.")
        
        elif args.command == "refine-md":
            refine_markdown(
                input_file=args.input,
                output_file=args.output,
                remove_headers=args.remove_headers,
                remove_footers=args.remove_footers,
                remove_page_numbers=args.remove_page_numbers,
                remove_toc=args.remove_toc,
                advanced_cleanup=args.advanced_cleanup,
                convert_tables=args.convert_tables,
                fix_toc_formatting=args.fix_toc_formatting,
                fix_toc_tables=args.fix_toc_tables,
                fix_internal_headings=args.fix_internal_headings,
                remove_page_markers=args.remove_page_markers,
                collapse_blank_lines=args.collapse_blank_lines,
                verbose=args.verbose
            )
            if args.verbose:
                print("Markdown refinement complete.")
        
        elif args.command == "pipeline":
            # Generate temp file name if not provided
            temp_file = args.temp_file
            if not temp_file:
                temp_file = args.output + ".temp.md"
            
            # First convert PDF to MD
            convert_pdf_to_md(args.input, temp_file, verbose=args.verbose)
            if args.verbose:
                print(f"PDF converted to temporary Markdown file: {temp_file}")
            
            # Then refine the Markdown
            refine_markdown(
                input_file=temp_file,
                output_file=args.output,
                remove_headers=args.remove_headers,
                remove_footers=args.remove_footers,
                remove_page_numbers=args.remove_page_numbers,
                remove_toc=args.remove_toc,
                advanced_cleanup=args.advanced_cleanup,
                convert_tables=args.convert_tables,
                fix_toc_formatting=args.fix_toc_formatting,
                fix_toc_tables=args.fix_toc_tables,
                fix_internal_headings=args.fix_internal_headings,
                remove_page_markers=args.remove_page_markers,
                collapse_blank_lines=args.collapse_blank_lines,
                verbose=args.verbose
            )
            
            # Clean up temp file if not explicitly requested
            if not args.temp_file:
                try:
                    os.remove(temp_file)
                    if args.verbose:
                        print(f"Removed temporary file: {temp_file}")
                except:
                    if args.verbose:
                        print(f"Warning: Could not remove temporary file: {temp_file}")
            
            if args.verbose:
                print("PDF to clean Markdown pipeline complete.")
        
        sys.exit(0)
    
    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()



