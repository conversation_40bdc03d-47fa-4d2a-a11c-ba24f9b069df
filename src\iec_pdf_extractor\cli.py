"""
Unified CLI interface for the IEC PDF Extractor toolchain.
"""

import argparse
import os
import sys
import tempfile
from .pdf_to_md import convert_pdf_to_md
from .md_refiner import refine_markdown

def main():
    """Main entry point for the IEC PDF Extractor CLI."""
    parser = argparse.ArgumentParser(
        description="IEC PDF Extractor - Convert PDF files to clean Markdown"
    )
    
    # Create subparsers for each command
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # PDF to MD command with advanced PyMuPDF features
    pdf_parser = subparsers.add_parser("pdf-to-md", help="Convert PDF to Markdown with advanced features")
    pdf_parser.add_argument("input", help="Input PDF file")
    pdf_parser.add_argument("output", help="Output Markdown file")
    pdf_parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    # PyMuPDF extraction options
    pdf_parser.add_argument("--extract-tables", action="store_true", default=True, help="Extract tables and save as CSV (default: True)")
    pdf_parser.add_argument("--no-extract-tables", action="store_true", help="Disable table extraction")
    pdf_parser.add_argument("--extract-images", action="store_true", default=True, help="Extract images (default: True)")
    pdf_parser.add_argument("--no-extract-images", action="store_true", help="Disable image extraction")
    pdf_parser.add_argument("--extract-annotations", action="store_true", default=True, help="Extract annotations and links (default: True)")
    pdf_parser.add_argument("--no-extract-annotations", action="store_true", help="Disable annotation extraction")
    pdf_parser.add_argument("--extract-metadata", action="store_true", default=True, help="Extract document metadata (default: True)")
    pdf_parser.add_argument("--no-extract-metadata", action="store_true", help="Disable metadata extraction")

    # Text extraction format options
    pdf_parser.add_argument("--text-format", choices=["text", "dict", "json", "xml", "html", "blocks", "words"],
                           default="text", help="Text extraction format (default: text)")

    # Output control options
    pdf_parser.add_argument("--output-dir", help="Directory for extracted files (CSV, images). Default: same as output file")
    pdf_parser.add_argument("--include-tables-in-md", action="store_true", default=True, help="Include table references in markdown (default: True)")
    pdf_parser.add_argument("--no-include-tables-in-md", action="store_true", help="Exclude table references from markdown")
    pdf_parser.add_argument("--include-images-in-md", action="store_true", default=True, help="Include image references in markdown (default: True)")
    pdf_parser.add_argument("--no-include-images-in-md", action="store_true", help="Exclude image references from markdown")
    pdf_parser.add_argument("--include-annotations-in-md", action="store_true", default=True, help="Include annotations in markdown (default: True)")
    pdf_parser.add_argument("--no-include-annotations-in-md", action="store_true", help="Exclude annotations from markdown")
    pdf_parser.add_argument("--include-links-in-md", action="store_true", default=True, help="Include links in markdown (default: True)")
    pdf_parser.add_argument("--no-include-links-in-md", action="store_true", help="Exclude links from markdown")
    pdf_parser.add_argument("--include-metadata-in-md", action="store_true", default=True, help="Include metadata in markdown (default: True)")
    pdf_parser.add_argument("--no-include-metadata-in-md", action="store_true", help="Exclude metadata from markdown")
    
    # MD Refiner command
    md_parser = subparsers.add_parser("refine-md", help="Refine Markdown files")
    md_parser.add_argument("input", help="Input Markdown file")
    md_parser.add_argument("output", help="Output Markdown file")
    md_parser.add_argument("--remove-headers", action="store_true", help="Remove repeated headers")
    md_parser.add_argument("--remove-footers", action="store_true", help="Remove repeated footers")
    md_parser.add_argument("--remove-page-numbers", action="store_true", help="Remove page numbers")
    md_parser.add_argument("--remove-toc", action="store_true", help="Remove table of contents")
    md_parser.add_argument("--fix-toc-formatting", action="store_true", help="Fix broken TOC lines")
    md_parser.add_argument("--fix-toc-tables", action="store_true", help="Clean up table-like entries in TOC")
    md_parser.add_argument("--fix-internal-headings", action="store_true", help="Join section numbers with titles")
    md_parser.add_argument("--convert-tables", action="store_true", help="Convert text tables to Markdown")
    md_parser.add_argument("--remove-page-markers", action="store_true", help="Remove page markers")
    md_parser.add_argument("--collapse-blank-lines", action="store_true", help="Collapse multiple blank lines")
    md_parser.add_argument("--advanced-cleanup", action="store_true", help="Apply advanced regex-based cleanup")
    md_parser.add_argument("--extract-key-sections", action="store_true", help="Extract only sections 6-9 (Scope through Clauses)")
    md_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    # Pipeline command (convert PDF and then refine) with advanced features
    pipeline_parser = subparsers.add_parser("pipeline", help="Convert PDF to Markdown with advanced features and then refine it")
    pipeline_parser.add_argument("input", help="Input PDF file")
    pipeline_parser.add_argument("output", help="Output Markdown file")
    pipeline_parser.add_argument("--temp-file", help="Temporary file for intermediate Markdown")
    pipeline_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")

    # PyMuPDF extraction options for pipeline
    pipeline_parser.add_argument("--extract-tables", action="store_true", default=True, help="Extract tables and save as CSV (default: True)")
    pipeline_parser.add_argument("--no-extract-tables", action="store_true", help="Disable table extraction")
    pipeline_parser.add_argument("--extract-images", action="store_true", default=True, help="Extract images (default: True)")
    pipeline_parser.add_argument("--no-extract-images", action="store_true", help="Disable image extraction")
    pipeline_parser.add_argument("--extract-annotations", action="store_true", default=True, help="Extract annotations and links (default: True)")
    pipeline_parser.add_argument("--no-extract-annotations", action="store_true", help="Disable annotation extraction")
    pipeline_parser.add_argument("--extract-metadata", action="store_true", default=True, help="Extract document metadata (default: True)")
    pipeline_parser.add_argument("--no-extract-metadata", action="store_true", help="Disable metadata extraction")
    pipeline_parser.add_argument("--text-format", choices=["text", "dict", "json", "xml", "html", "blocks", "words"],
                                default="text", help="Text extraction format (default: text)")
    pipeline_parser.add_argument("--output-dir", help="Directory for extracted files (CSV, images). Default: same as output file")

    # Markdown refinement options
    pipeline_parser.add_argument("--remove-headers", action="store_true", help="Remove repeated headers")
    pipeline_parser.add_argument("--remove-footers", action="store_true", help="Remove repeated footers")
    pipeline_parser.add_argument("--remove-page-numbers", action="store_true", help="Remove page numbers")
    pipeline_parser.add_argument("--remove-toc", action="store_true", help="Remove table of contents")
    pipeline_parser.add_argument("--fix-toc-formatting", action="store_true", help="Fix broken TOC lines")
    pipeline_parser.add_argument("--fix-toc-tables", action="store_true", help="Clean up table-like entries in TOC")
    pipeline_parser.add_argument("--fix-internal-headings", action="store_true", help="Join section numbers with titles")
    pipeline_parser.add_argument("--convert-tables", action="store_true", help="Convert text tables to Markdown")
    pipeline_parser.add_argument("--remove-page-markers", action="store_true", help="Remove page markers")
    pipeline_parser.add_argument("--collapse-blank-lines", action="store_true", help="Collapse multiple blank lines")
    pipeline_parser.add_argument("--advanced-cleanup", action="store_true", help="Apply advanced regex-based cleanup")
    pipeline_parser.add_argument("--extract-key-sections", action="store_true", help="Extract only sections 6-9 (Scope through Clauses)")

    # Table extraction command (specialized for table extraction only)
    table_parser = subparsers.add_parser("extract-tables", help="Extract tables from PDF and save as CSV files")
    table_parser.add_argument("input", help="Input PDF file")
    table_parser.add_argument("--output-dir", help="Directory to save CSV files. Default: same as input file directory")
    table_parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    table_parser.add_argument("--create-summary", action="store_true", help="Create a summary markdown file listing all tables")

    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        sys.exit(1)
    
    try:
        if args.command == "pdf-to-md":
            # Handle negative flags
            extract_tables = args.extract_tables and not args.no_extract_tables
            extract_images = args.extract_images and not args.no_extract_images
            extract_annotations = args.extract_annotations and not args.no_extract_annotations
            extract_metadata = args.extract_metadata and not args.no_extract_metadata
            include_tables_in_md = args.include_tables_in_md and not args.no_include_tables_in_md
            include_images_in_md = args.include_images_in_md and not args.no_include_images_in_md
            include_annotations_in_md = args.include_annotations_in_md and not args.no_include_annotations_in_md
            include_links_in_md = args.include_links_in_md and not args.no_include_links_in_md
            include_metadata_in_md = args.include_metadata_in_md and not args.no_include_metadata_in_md

            results = convert_pdf_to_md(
                input_file=args.input,
                output_file=args.output,
                verbose=args.verbose,
                extract_tables=extract_tables,
                extract_images=extract_images,
                extract_annotations=extract_annotations,
                extract_metadata=extract_metadata,
                text_format=args.text_format,
                output_dir=args.output_dir,
                include_tables_in_md=include_tables_in_md,
                include_images_in_md=include_images_in_md,
                include_annotations_in_md=include_annotations_in_md,
                include_links_in_md=include_links_in_md,
                include_metadata_in_md=include_metadata_in_md
            )

            if args.verbose:
                print("Advanced PDF to Markdown conversion complete.")
                print(f"Results summary: {results['pages_processed']} pages, {results['tables_found']} tables, {results['images_found']} images")
        
        elif args.command == "refine-md":
            refine_markdown(
                input_file=args.input,
                output_file=args.output,
                remove_headers=args.remove_headers,
                remove_footers=args.remove_footers,
                remove_page_numbers=args.remove_page_numbers,
                remove_toc=args.remove_toc,
                advanced_cleanup=args.advanced_cleanup,
                convert_tables=args.convert_tables,
                fix_toc_formatting=args.fix_toc_formatting,
                fix_toc_tables=args.fix_toc_tables,
                fix_internal_headings=args.fix_internal_headings,
                remove_page_markers=args.remove_page_markers,
                collapse_blank_lines=args.collapse_blank_lines,
                verbose=args.verbose
            )
            if args.verbose:
                print("Markdown refinement complete.")
        
        elif args.command == "pipeline":
            # Handle negative flags for extraction options
            extract_tables = args.extract_tables and not args.no_extract_tables
            extract_images = args.extract_images and not args.no_extract_images
            extract_annotations = args.extract_annotations and not args.no_extract_annotations
            extract_metadata = args.extract_metadata and not args.no_extract_metadata

            # Generate temp file name if not provided
            temp_file = args.temp_file
            if not temp_file:
                temp_file = args.output + ".temp.md"

            # First convert PDF to MD with advanced features
            results = convert_pdf_to_md(
                input_file=args.input,
                output_file=temp_file,
                verbose=args.verbose,
                extract_tables=extract_tables,
                extract_images=extract_images,
                extract_annotations=extract_annotations,
                extract_metadata=extract_metadata,
                text_format=args.text_format,
                output_dir=args.output_dir,
                include_tables_in_md=True,  # Include everything in temp file
                include_images_in_md=True,
                include_annotations_in_md=True,
                include_links_in_md=True,
                include_metadata_in_md=True
            )

            if args.verbose:
                print(f"PDF converted to temporary Markdown file: {temp_file}")
                print(f"Extraction results: {results['pages_processed']} pages, {results['tables_found']} tables, {results['images_found']} images")

            # Then refine the Markdown
            refine_markdown(
                input_file=temp_file,
                output_file=args.output,
                remove_headers=args.remove_headers,
                remove_footers=args.remove_footers,
                remove_page_numbers=args.remove_page_numbers,
                remove_toc=args.remove_toc,
                advanced_cleanup=args.advanced_cleanup,
                convert_tables=args.convert_tables,
                fix_toc_formatting=args.fix_toc_formatting,
                fix_toc_tables=args.fix_toc_tables,
                fix_internal_headings=args.fix_internal_headings,
                remove_page_markers=args.remove_page_markers,
                collapse_blank_lines=args.collapse_blank_lines,
                extract_key_sections=args.extract_key_sections,
                verbose=args.verbose
            )

            # Clean up temp file if not explicitly requested
            if not args.temp_file:
                try:
                    os.remove(temp_file)
                    if args.verbose:
                        print(f"Removed temporary file: {temp_file}")
                except:
                    if args.verbose:
                        print(f"Warning: Could not remove temporary file: {temp_file}")

            if args.verbose:
                print("Advanced PDF to clean Markdown pipeline complete.")
                print(f"Final output: {args.output}")
                if results['csv_files']:
                    print(f"CSV files created: {', '.join(results['csv_files'])}")
                if results['image_files']:
                    print(f"Image files created: {', '.join(results['image_files'])}")

        elif args.command == "extract-tables":
            # Specialized table extraction command
            from .pdf_to_md.parser import parse_pdf

            output_dir = args.output_dir
            if output_dir is None:
                output_dir = os.path.dirname(args.input) or "."

            if args.verbose:
                print(f"Extracting tables from: {args.input}")
                print(f"Output directory: {output_dir}")

            # Extract only tables
            pdf_data = parse_pdf(
                args.input,
                verbose=args.verbose,
                extract_tables=True,
                extract_images=False,
                extract_annotations=False,
                extract_metadata=False,
                text_format="text",
                output_dir=output_dir
            )

            tables = pdf_data.get("tables", [])

            if args.verbose:
                print(f"Found {len(tables)} tables in the document")

            # Create summary if requested
            if args.create_summary and tables:
                summary_file = os.path.join(output_dir, "tables_summary.md")
                from .pdf_to_md.converter import _generate_tables_summary
                summary_content = _generate_tables_summary(tables, args.verbose)

                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Tables Extracted from {os.path.basename(args.input)}\n\n")
                    f.write(summary_content)

                if args.verbose:
                    print(f"Created summary file: {summary_file}")

            if args.verbose:
                print("Table extraction complete.")
                csv_files = [table.get("csv_file") for table in tables if table.get("csv_file")]
                if csv_files:
                    print(f"CSV files created: {', '.join(csv_files)}")

        sys.exit(0)
    
    except Exception as e:
        print(f"[ERROR] >> {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()



